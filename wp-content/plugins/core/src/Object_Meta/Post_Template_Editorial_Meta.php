<?php
declare( strict_types=1 );

namespace Tribe\Project\Object_Meta;

use Tribe\Libs\ACF\ACF_Meta_Group;
use Tribe\Libs\ACF\Field;
use Tribe\Libs\ACF\Field_Group;
use Tribe\Libs\ACF\Group;
use Tribe\Project\Post_Types\Post\Post_Template_Editorial;
use Tribe\Project\Templates\Components\Traits\With_Lottie_Fields;

class Post_Template_Editorial_Meta extends ACF_Meta_Group {
	use With_Lottie_Fields;

	public const NAME = 'post_template_editorial_meta';

	public const HIDE_NAVIGATION         = 'hide_navigation';
	public const HEADER_DARK_BACKGROUND  = 'header_dark_background';
	public const HEADER_BACKGROUND_IMAGE = 'header_background_image';
	public const HEADER_CTA              = 'header_cta';
	public const HEADER_NEWSLETTER_URL   = 'header_newsletter_url';
	public const CHAPTER_SIDEBAR         = 'chapter_sidebar';
	public const TABLE_CONTENTS_TITLE    = 'table_contents_title';
	public const HIDE_NEWSLETTER         = 'hide_newsletter';
	public const NEWSLETTER_URL          = 'newsletter_url';

	public const MEDIA_TYPE = 'media_type';
	public const IMAGE      = 'image';

	public const LOTTIE_JSON_URL = 'lottie_json_url';
	public const LOTTIE_DATA     = 'lottie_data';

	public const TABLE_CONTENTS_TITLE_DEFAULT = 'Table of contents';

	public function get_keys() {
		return [
			static::HIDE_NAVIGATION,
			static::HEADER_DARK_BACKGROUND,
			static::HEADER_BACKGROUND_IMAGE,
			static::HEADER_CTA,
			static::HEADER_NEWSLETTER_URL,
			static::CHAPTER_SIDEBAR,
			static::TABLE_CONTENTS_TITLE,
			static::HIDE_NEWSLETTER,
			static::NEWSLETTER_URL,
		];
	}

	public function get_value( $key, $post_id = 'option' ) {
		return parent::get_value( $post_id, $key );
	}

	public function get_group_config(): array {
		$group = new Group( self::NAME, $this->object_types );
		$group->set( 'title', __( 'Editorial Post Options', 'tribe' ) );
		$group->set( 'location', [
			[
				[
					'param'    => 'post_type',
					'operator' => '==',
					'value'    => Post_Template_Editorial::POST_TYPE,
				],
				[
					'param'    => 'post_template',
					'operator' => '==',
					'value'    => Post_Template_Editorial::TEMPLATE_FILE,
				],
			],
		] );

		$group->add_field( $this->get_hide_header_field() );
		$group->add_field( $this->get_header_background_media_type() );
		$group->add_field( $this->get_header_background_image_field() );
		$group->add_field( $this->get_header_background_lottie_animation_field() );
		$group->add_field( $this->get_header_has_dark_background_field() );
		$group->add_field( $this->get_header_cta_field() );
		$group->add_field( $this->get_editorial_newsletter_url_field() );
		$group->add_field( $this->get_chapter_sidebar_options() );

		return $group->get_attributes();
	}

	/**
	 * Hide the header of the post
	 *
	 * @return Field
	 */
	private function get_hide_header_field(): Field {
		$field = new Field( self::NAME . '_' . self::HIDE_NAVIGATION );
		$field->set_attributes( [
			'label'         => __( 'Hide Site Navigation?', 'tribe' ),
			'name'          => self::HIDE_NAVIGATION,
			'instructions'  => __( 'Hide the navigation bar from the site.', 'tribe' ),
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
		] );

		return $field;
	}

	/**
	 * Header Background Media Type
	 *
	 * @return Field
	 */
	private function get_header_background_media_type(): Field {
		$field = new Field( self::NAME . '_' . self::MEDIA_TYPE );
		$field->set_attributes( [
			'label'         => __( 'Header Background Media Type', 'tribe' ),
			'name'          => self::MEDIA_TYPE,
			'type'          => 'radio',
			'choices'       => [
				self::IMAGE  => __( 'Image', 'tribe' ),
				self::LOTTIE => __( 'Lottie Animation', 'tribe' ),
			],
			'default_value' => [
				self::IMAGE,
			],
		] );

		return $field;
	}

	/**
	 * Image for headers background
	 *
	 * @return Field
	 */
	private function get_header_background_image_field(): Field {
		$field = new Field( self::NAME . '_' . self::HEADER_BACKGROUND_IMAGE );
		$field->set_attributes( [
			'label'             => __( 'Header Background Image', 'tribe' ),
			'name'              => self::HEADER_BACKGROUND_IMAGE,
			'instructions'      => __( 'Hide the navigation bar from the site.<br>(Min size: 1920 x 960px)', 'tribe' ),
			'type'              => 'image',
			'return_format'     => 'id',
			'min_width'         => 1920,
			'min_height'        => 960,
			'conditional_logic' => [
				[
					[
						'field'    => 'field_' . self::NAME . '_' . self::MEDIA_TYPE,
						'operator' => '==',
						'value'    => self::IMAGE,
					],
				],
			],
		] );

		return $field;
	}

	/**
	 * Lottie Animation for headers background
	 *
	 * @return Field
	 */
	private function get_header_background_lottie_animation_field(): Field {
		$field = $this->get_lottie_fields( self::NAME . '_' . self::MEDIA_TYPE, self::LOTTIE, true );

		return $field;
	}

	/**
	 * @return Field
	 */
	private function get_header_has_dark_background_field(): Field {
		$field = new Field( self::NAME . '_' . self::HEADER_DARK_BACKGROUND );
		$field->set_attributes( [
			'label'         => __( 'Header Has a Dark Background?', 'tribe' ),
			'name'          => self::HEADER_DARK_BACKGROUND,
			'instructions'  => __( 'Is the background image dark?.', 'tribe' ),
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
		] );

		return $field;
	}

	/**
	 * @return Field
	 */
	private function get_header_cta_field(): Field {
		$field = new Field( self::NAME . '_' . self::HEADER_CTA );
		$field->set_attributes( [
			'label'        => __( 'Header CTA', 'tribe' ),
			'name'         => self::HEADER_CTA,
			'instructions' => __( 'If empty, the default "Read Now" will be added to the header.', 'tribe' ),
			'type'         => 'link',
		] );

		return $field;
	}

	/**
	 * @return Field
	 */
	private function get_editorial_newsletter_url_field(): Field {
		$field = new Field( self::NAME . '_' . self::NEWSLETTER_URL );
		$field->set_attributes( [
			'label'        => __( 'Newsletter URL', 'tribe' ),
			'name'         => self::NEWSLETTER_URL,
			'instructions' => __( 'The url of the newsletter link for the post', 'tribe' ),
			'type'         => 'url',
		] );

		return $field;
	}

	/**
	 * Hide the header of the post
	 *
	 * @return Field
	 */
	private function get_chapter_sidebar_options(): Field {
		$field = new Field_Group( self::NAME . '_' . self::CHAPTER_SIDEBAR );
		$field->set_attributes( [
			'label' => __( 'Chapter Sidebar', 'tribe' ),
			'name'  => self::CHAPTER_SIDEBAR,
			'type'  => 'group',
		] );

		$field->add_field( $this->get_chapter_sidebar_table_of_contents_title() );
		$field->add_field( $this->get_chapter_sidebar_hide_newsletter_field() );

		return $field;
	}

	/**
	 * @return Field
	 */
	private function get_chapter_sidebar_table_of_contents_title(): Field {
		$field = new Field( self::NAME . '_' . self::TABLE_CONTENTS_TITLE );
		$field->set_attributes( [
			'label'        => __( 'Title of the Table of Contents', 'tribe' ),
			'name'         => self::TABLE_CONTENTS_TITLE,
			'instructions' => __( 'If empty will be used the default: "' . self::TABLE_CONTENTS_TITLE_DEFAULT . '"', 'tribe' ),
			'type'         => 'text',
			'maxlength'    => '45',
		] );

		return $field;
	}

	/**
	 * @return Field
	 */
	private function get_chapter_sidebar_hide_newsletter_field(): Field {
		$field = new Field( self::NAME . '_' . self::HIDE_NEWSLETTER );
		$field->set_attributes( [
			'label'         => __( 'Hide Newsletter', 'tribe' ),
			'name'          => self::HIDE_NEWSLETTER,
			'instructions'  => __( 'Hide the newsletter CTA on the chapter sidebar.', 'tribe' ),
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
		] );

		return $field;
	}
}
