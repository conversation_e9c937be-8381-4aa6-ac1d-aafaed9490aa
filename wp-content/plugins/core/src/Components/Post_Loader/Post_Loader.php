<?php

namespace Tribe\Project\Components\Post_Loader;

use Tribe\Project\Blocks\Types\Advanced_Content_Filter\Advanced_Content_Filter;
use Tribe\Project\Blocks\Types\Content_Loop\Content_Loop;

class Post_Loader {
	public const NONCE_FIELD_NAME   = 'nonce';
	public const NONCE_ACTION       = 'ajax_post_loader';
	public const BLOCK_FIELD_NAME   = 'block';
	public const POST_FIELD_NAME    = 'pid';
	public const PAGE_FIELD_NAME    = 'page';
	public const FILTERS_FIELD_NAME = 'filters';
	public const SUPPORTED_BLOCKS   = [ Content_Loop::NAME, Advanced_Content_Filter::NAME ];

	public function load_ajax_posts() {
		$nonce           = sanitize_text_field( (string) $_POST[ self::NONCE_FIELD_NAME ] ?: '' );
		$block_unique_id = sanitize_text_field( (string) $_POST[ self::BLOCK_FIELD_NAME ] ?: '' );
		$post_id         = (int) sanitize_text_field( $_POST[ self::POST_FIELD_NAME ] ?: '' );

		if ( ! wp_verify_nonce( $nonce, self::NONCE_ACTION ) ) {
			wp_send_json_error( 'Invalid request.' );
			exit;
		}

		if ( ! $post = get_post( $post_id ) ) {
			wp_send_json_error( 'Post not found.' );
			exit;
		}

		if ( ! $blocks = parse_blocks( $post->post_content ) ) {
			wp_send_json_error( 'The post doesn\'t have blocks.' );
			exit;
		}

		if ( ! $block = $this->find_post_loader_block_data_by_id( $blocks, $block_unique_id ) ) {
			wp_send_json_error( 'Block ID not found.' );
			exit;
		}

		$supported_blocks = array_map( function ( $value ) {
			return 'acf/' . $value;
		}, self::SUPPORTED_BLOCKS );

		if ( in_array( $block['blockName'], $supported_blocks, true ) ) {
			echo render_block( $block );
			wp_die();
		}

		wp_send_json_error( 'Unsupported block.' );
		exit;
	}

	private function find_post_loader_block_data_by_id( array $blocks, string $block_unique_id ): ?array {
		foreach ( $blocks as $block ) {
			if ( ! empty( $block['attrs'] ) && isset( $block['attrs']['uniqid'] ) && $block['attrs']['uniqid'] === $block_unique_id ) {
				return $block;
			}

			if ( ! empty( $block['innerBlocks'] ) ) {
				$data = $this->find_post_loader_block_data_by_id( $block['innerBlocks'], $block_unique_id );

				if ( $data !== null ) {
					return $data;
				}
			}
		}

		return null;
	}
}
