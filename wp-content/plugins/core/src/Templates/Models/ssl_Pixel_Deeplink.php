<?php

namespace Tribe\Project\Templates\Models;

/*
 * ssl_Pixel_Deeplink
 *
 * This pixel is used for pages using {{deeplink}}.
 *
 * This pixel is automatically added to the footer of the page.
 *
 * This will render a pixel like this:
 * https://crozdesk.com/tracker/v2/{partner}/{page_type}/{array-of-pids}/impression.png
 */

class ssl_Pixel_Deeplink extends ssl_Pixel_Generate {
	protected static bool $ssl_showed_pixel_page_impression;

	public function __construct() {
		parent::__construct();

		add_action( 'wp_footer', [ $this, 'add_pixel_page_to_content' ], 1 );
	}

	public function add_pixel_page_to_content(): void {
		if ( parent::hide_pixel() ) {
			return;
		}

		if ( ! empty( $this->ssl_pixel_page_loaded_pids ) ) {
			if ( ! isset( self::$ssl_showed_pixel_page_impression ) ) {
				self::$ssl_showed_pixel_page_impression = true;

				$impression_pixel = parent::get_ssl_pixel_page_impression( 'deeplink' ) ?? '';

				echo $impression_pixel;
			}
		}
	}
}
