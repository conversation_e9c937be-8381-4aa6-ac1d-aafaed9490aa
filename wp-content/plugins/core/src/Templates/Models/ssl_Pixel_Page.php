<?php

namespace Tribe\Project\Templates\Models;

/*
 * ssl_Pixel_Page
 *
 * This pixel is used for pages using blocks like Checklists, Pros and Cons, etc.
 *
 * This pixel is automatically added to the footer of the page.
 *
 * This will render a pixel like this:
 * https://crozdesk.com/tracker/v2/{partner}/{page_type}/{array-of-pids}/impression.png
 * https://crozdesk.com/tracker/v2/{partner}/p/{page_type}/{array-of-pids}/conversion.png
 */

use Tribe\Project\Post_Types\Tool_Post\Tool_Post;

class ssl_Pixel_Page extends ssl_Pixel_Generate {
	protected static bool $ssl_showed_pixel_page_impression;
	protected static bool $ssl_showed_pixel_page_conversion;

	public function __construct() {
		parent::__construct();

		add_action( 'wp_footer', [ $this, 'add_pixel_page_to_content' ], 1 );
	}

	public function add_pixel_page_to_content(): void {
		if ( parent::hide_pixel() ) {
			return;
		}

		$pids_unique = array_unique( $this->ssl_pixel_page_loaded_pids );
		$page_type = count( $pids_unique ) > 1 ? 'comp' : 'review';

		if ( is_single() && Tool_Post::NAME === get_post_type() ) {
			if ( ! isset( self::$ssl_showed_pixel_page_impression ) ) {
				self::$ssl_showed_pixel_page_impression = true;

				$impression_pixel = parent::get_ssl_pixel_page_impression( $page_type ) ?? '';

				echo $impression_pixel;
			}

			if ( ! isset( self::$ssl_showed_pixel_page_conversion ) ) {
				self::$ssl_showed_pixel_page_conversion = true;

				$conversion_pixel = parent::get_ssl_pixel_page_conversion( $page_type ) ?? '';

				echo $conversion_pixel;
			}
		}
	}
}
