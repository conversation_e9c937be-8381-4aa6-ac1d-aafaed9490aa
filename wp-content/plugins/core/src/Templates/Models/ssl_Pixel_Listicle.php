<?php

namespace Tribe\Project\Templates\Models;

use Tribe\Project\Templates\Components\Traits\With_Get_Current_Url;
use Tribe\Project\Users\Roles;

/*
 * ssl_Pixel_Listicle
 *
 * This pixel is used for pages using blocks like Shortlists, Full Listing, Overflow and Summary Table.
 *
 * This pixel need to be added to the content of the page.
 *
 * To add multiple pixels you need to use the $reset_pixel parameter in the constructor.
 *
 * This will render a pixel like this:
 * listicle_impression_url (deprecated): https://crozdesk.com/tracker/v2/tracker/v2/{cid}/{array-of-pids}/impression.png
 * page_impression_url: https://crozdesk.com/tracker/v2/{partner}/c/{category_id}/{array-of-pids}/impression.png
 * partner_impression_url: https://crozdesk.com/tracker/v2/p/{partner}/c/{category_id}/{array-of-pids}/impression.png
 */

class ssl_Pixel_Listicle {
	use With_Get_Current_Url;

	public const PIXEL_TYPE_IMPRESSION = 'impression';
	public const PIXEL_TYPE_CONVERSION = 'conversion';

	protected static array  $ssl_pixel_listicle_loaded_pids;
	protected static string $ssl_pixel_listicle_cid;
	protected static bool   $ssl_pixel_skip_view;
	protected static bool   $ssl_showed_pixel_listicle_impression;
	protected static bool   $ssl_showed_pixel_listicle_conversion;
	protected static string $ssl_kv_key;

	/**
	 * @param $cid         string The cid of the listicle.
	 * @param $reset_pixel bool If true, the list of pids will be reset and another pixel will be rendered.
	 */
	public function __construct( string $cid, bool $reset_pixel = false ) {
		self::$ssl_pixel_skip_view = false;

		if ( ! isset( self::$ssl_pixel_listicle_loaded_pids ) || true === $reset_pixel ) {
			self::$ssl_pixel_listicle_cid               = $cid;
			self::$ssl_showed_pixel_listicle_impression = false;
			self::$ssl_showed_pixel_listicle_conversion = false;
			self::$ssl_pixel_listicle_loaded_pids       = [];
		}
	}

	public function get_country_code(): ?string {
		if ( ! isset( $_SERVER['HTTP_GEOIP_COUNTRY_CODE'] ) ) {
			return null;
		}

		return $_SERVER['HTTP_GEOIP_COUNTRY_CODE'];
	}

	public function hide_pixel(): bool {
		$is_admin_user = Roles::is_administrator() || Roles::is_editor() || Roles::is_content_manager() || Roles::is_api();

		return is_admin() || $is_admin_user;
	}

	public function add_impression_pixel_listicle_to_content(): void {
		if ( $this->hide_pixel() ) {
			return;
		}

		echo self::get_ssl_pixel_listicle_impression() ?? '';
	}

	public function add_conversion_pixel_listicle_to_content(): void {
		if ( $this->hide_pixel() ) {
			return;
		}

		echo self::get_ssl_pixel_listicle_conversion() ?? '';
	}

	public function get_ssl_pixel_listicle_impression(): ?string {
		if ( isset( self::$ssl_showed_pixel_listicle_impression ) && self::$ssl_showed_pixel_listicle_impression === false ) {
			self::$ssl_showed_pixel_listicle_impression = true;
			$site_abbreviation                          = strtolower( get_option( 'bawz_site_abbreviation' ) ?? '' );

			[
				$ssl_pixel_url,
				$pids_url,
				$params_starter,
				$params,
			] = $this->get_ssl_pixel_listicle_generator( self::PIXEL_TYPE_IMPRESSION );

			// impression_url
			//return "<img src='$ssl_pixel_url/" . self::$ssl_pixel_listicle_cid . "/$pids_url/impression.png$params_starter$params' style='position:absolute; visibility:hidden' class='crozdesk-pixel crozdesk-pixel-listicle crozdesk-pixel-listicle-i'>";

			// partner_impression_url
			return "<img src='$ssl_pixel_url/p/$site_abbreviation/c/" . self::$ssl_pixel_listicle_cid . "/$pids_url/impression.png$params_starter$params' style='position:absolute; visibility:hidden; width: 0px; height: 0px;' class='crozdesk-pixel crozdesk-pixel-listicle crozdesk-pixel-listicle-i'>";
		}

		return null;
	}

	public function get_ssl_pixel_listicle_conversion(): ?string {
		if ( isset( self::$ssl_showed_pixel_listicle_conversion ) && self::$ssl_showed_pixel_listicle_conversion === false ) {
			self::$ssl_showed_pixel_listicle_conversion = true;
			$site_abbreviation                          = strtolower( get_option( 'bawz_site_abbreviation' ) ?? '' );

			if ( ! $site_abbreviation ) {
				return null;
			}

			[
				$ssl_pixel_url,
				$pids_url,
				$params_starter,
				$params,
			] = $this->get_ssl_pixel_listicle_generator( self::PIXEL_TYPE_CONVERSION );

			return "<img src='$ssl_pixel_url/$site_abbreviation/c/" . self::$ssl_pixel_listicle_cid . "/$pids_url/conversion.png$params_starter$params' style='position:absolute; visibility:hidden; width: 0px; height: 0px;' class='crozdesk-pixel crozdesk-pixel-listicle crozdesk-pixel-listicle-c'>";
		}

		return null;
	}

	public function set_ssl_pixel_listicle_loaded_pids( array $pids, bool $skipview = false ): void {
		self::$ssl_pixel_skip_view            = $skipview;
		self::$ssl_pixel_listicle_loaded_pids = array_merge( self::$ssl_pixel_listicle_loaded_pids, $pids );
	}

	public function set_ssl_pixel_listicle_loaded_mkey( string $key ): void {
		self::$ssl_kv_key = $key;
	}

	public function get_ssl_pixel_listicle_generator( $type ): array {
		$pids_unique = array_unique( self::$ssl_pixel_listicle_loaded_pids );
		$pids_url    = implode( '-', $pids_unique );

		$params_starter = '';
		$params_array   = [];
		$params         = '';
		$ssl_pixel_url  = '';

		if ( 'production' === wp_get_environment_type() ) {
			if ( self::PIXEL_TYPE_IMPRESSION === $type ) {
				$ssl_pixel_url     = "https://impression.crozdesk.com/tracker/v2";
				$params_array['r'] = $this->get_current_url();
			} elseif ( self::PIXEL_TYPE_CONVERSION === $type ) {
				$ssl_pixel_url = "https://crozdesk.com/tracker/v2";
			}
		} else {
			if ( self::PIXEL_TYPE_IMPRESSION === $type ) {
				$ssl_pixel_url     = "https://impression-staging.crozdesk.com/tracker/v2";
				$params_array['r'] = $this->get_current_url();
			} elseif ( self::PIXEL_TYPE_CONVERSION === $type ) {
				$ssl_pixel_url = "https://staging.crozdesk.com/tracker/v2";
			}
		}

		if ( self::$ssl_pixel_skip_view ) {
			$params_array['skipview'] = 'true';
		}

		if ( $geocode = $this->get_country_code() ) {
			$params_array['geo'] = $geocode;
		}

		if ( self::$ssl_kv_key ) {
			$params_array['language'] = $this->languageFromKVKey( self::$ssl_kv_key );
		}

		$site_abbreviation = strtolower( get_option( 'bawz_site_abbreviation' ) ?? '' );
		if ( $site_abbreviation ) {
			$params_array['wpp_id'] = $site_abbreviation . '_' . get_the_ID();
		}

		if ( count( $params_array ) > 0 ) {
			$params_starter = '?';
			$params         = http_build_query( $params_array );
		}

		return [ $ssl_pixel_url, $pids_url, $params_starter, $params ];
	}

	protected function languageFromKVKey( string $key ): ?string {
		$splitKey = explode( '.', $key );

		return isset( $splitKey[3] ) ? mb_strtoupper( $splitKey[3] ) : 'EN';
	}
}
