<?php

namespace Tribe\Project\Templates\Models;

use Tribe\Project\Templates\Components\Traits\With_Get_Current_Url;
use Tribe\Project\Users\Roles;

abstract class ssl_Pixel_Generate {
	use With_Get_Current_Url;

	public const PIXEL_TYPE_IMPRESSION = 'impression';
	public const PIXEL_TYPE_CONVERSION = 'conversion';

	protected array         $ssl_pixel_page_loaded_pids;
	protected string        $ssl_pixel_page_partner;
	protected string        $current_url;
	protected bool          $ssl_pixel_skip_view;
	protected static string $site_abbreviation;

	public function __construct() {
		$this->ssl_pixel_skip_view = false;
		self::$site_abbreviation   = strtolower( get_option( 'bawz_site_abbreviation' ) ?? '' );

		if ( ! isset( $this->ssl_pixel_page_loaded_pids ) ) {
			$this->ssl_pixel_page_loaded_pids = [];
		}

		if ( ! isset( $this->current_url ) ) {
			$this->current_url = $this->get_current_url();
		}
	}

	public function hide_pixel(): bool {
		$is_admin_user = Roles::is_administrator() || Roles::is_editor() || Roles::is_content_manager() || Roles::is_api();

		return is_admin() || $is_admin_user;
	}

	public function set_ssl_pixel_page_loaded_pids( string $partner, array $pids ): void {
		if ( ! isset( $this->ssl_pixel_page_partner ) ) {
			$this->ssl_pixel_page_partner = strtolower( $partner );
		}

		$this->ssl_pixel_page_loaded_pids = array_merge( $this->ssl_pixel_page_loaded_pids, $pids );
	}

	public function pixel_generator( $type ): array {
		if ( 'production' === wp_get_environment_type() ) {
			if ( self::PIXEL_TYPE_IMPRESSION === $type ) {
				$ssl_pixel_url = "https://impression.crozdesk.com/tracker/v2";
			} elseif ( self::PIXEL_TYPE_CONVERSION === $type ) {
				$ssl_pixel_url = "https://crozdesk.com/tracker/v2";
			}
		} else {
			if ( self::PIXEL_TYPE_IMPRESSION === $type ) {
				$ssl_pixel_url = "https://impression-staging.crozdesk.com/tracker/v2";
			} elseif ( self::PIXEL_TYPE_CONVERSION === $type ) {
				$ssl_pixel_url = "https://staging.crozdesk.com/tracker/v2";
			}
		}

		$pids_unique = array_unique( $this->ssl_pixel_page_loaded_pids );
		$pids_url    = implode( '-', $pids_unique );

		$params_starter = "";
		$params_array   = [];
		$params         = "";

		if ( self::PIXEL_TYPE_IMPRESSION === $type ) {
			$params_array["r"] = $this->current_url;

			if ( $this->ssl_pixel_skip_view ) {
				$params_array["skipview"] = "true";
			}

			if ( $geocode = self::get_country_code() ) {
				$params_array["geo"] = $geocode;
			}

			if ( self::$site_abbreviation ) {
				$params_array["wpp_id"] = self::$site_abbreviation . "_" . get_the_ID();
			}
		}

		if ( count( $params_array ) > 0 ) {
			$params_starter = "?";
			$params         = http_build_query( $params_array );
		}

		return [ $ssl_pixel_url, $pids_url, $params_starter, $params ];
	}

	public function get_ssl_pixel_page_impression( string $page_type ): string {
		[
			$ssl_pixel_url,
			$pids_url,
			$params_starter,
			$params,
		] = $this->pixel_generator( self::PIXEL_TYPE_IMPRESSION );

		return "<img src='$ssl_pixel_url/" . $this->ssl_pixel_page_partner . "/$page_type/$pids_url/impression.png{$params_starter}{$params}' style='position:absolute; visibility:hidden; width: 0px; height: 0px;' class='crozdesk-pixel crozdesk-pixel-page crozdesk-pixel-page-i'>";
	}

	public function get_ssl_pixel_page_conversion( string $page_type ): string {
		[
			$ssl_pixel_url,
			$pids_url,
			$params_starter,
			$params,
		] = $this->pixel_generator( self::PIXEL_TYPE_CONVERSION );

		return "<img src='$ssl_pixel_url/" . $this->ssl_pixel_page_partner . "/p/$page_type/$pids_url/conversion.png{$params_starter}{$params}' style='position:absolute; visibility:hidden; width: 0px; height: 0px;' class='crozdesk-pixel crozdesk-pixel-page crozdesk-pixel-page-c'>";
	}

	public static function get_country_code(): ?string {
		return $_SERVER['HTTP_GEOIP_COUNTRY_CODE'] ?? null;
	}
}
