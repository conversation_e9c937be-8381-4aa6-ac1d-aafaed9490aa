<?php
declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\Traits;

use WP_Term;

trait Categories_Utilities {
	/**
	 * @param int          $post_id
	 * @param string|array $taxonomy_slugs
	 *
	 * @return WP_Term|null
	 */
	private function get_post_main_category( int $post_id, string|array $taxonomy_slugs ): WP_Term|null {
		if ( is_string( $taxonomy_slugs ) && function_exists( 'yoast_get_primary_term' ) && ( $yoast_primary_term_id = yoast_get_primary_term_id( $taxonomy_slugs, $post_id ) ) ) {
			$term = get_term( $yoast_primary_term_id );

			if ( ! $term || is_wp_error( $term ) ) {
				return null;
			}

			return $term;
		}

		if ( is_string( $taxonomy_slugs ) ) {
			$taxonomy_slugs = [ $taxonomy_slugs ];
		}

		foreach ( $taxonomy_slugs as $slug ) {
			$terms = get_the_terms( $post_id, $slug );

			if ( ! is_wp_error( $terms ) && $terms ) {
				return $terms[0];
			}
		}

		return null;
	}
}
