<?php

declare( strict_types=1 );

namespace Tribe\Project\Templates\Components;

use Tribe\Project\Admin\Editor\Blocks_Utilities;
use Tribe\Project\Components\Post_Loader\Post_Loader;
use Tribe\Project\Templates\Components\button\Button_Controller;
use Tribe\Project\Templates\Components\container\Container_Controller;

abstract class Post_Loader_Controller extends Abstract_Controller implements Post_Loader_Interface {
	public const BLOCK_ID = 'block_acf_id';

	public const CONTAINER_CLASS             = 'post-loader-container';
	public const LOAD_MORE_CLASS             = 'post-loader-load-more';
	public const FILTER_CLASS                = 'post-loader-filter';
	public const SPINNER_CLASS               = 'post-loader-spinner';
	public const DISABLE_INPUT_TRIGGER_CLASS = 'post-loader-filter--trigger-disabled';
	public const APPLY_FILTERS_CLASS         = 'post-loader-apply-filters';
	public const RESET_FILTERS_CLASS         = 'post-loader-reset-filters';

	private string $block_id;
	private bool   $more_posts;

	/**
	 * @param array $args
	 */
	public function __construct( array $args = [] ) {
		$args = $this->parse_args( $args );

		$this->block_id = ! empty( $args[ self::BLOCK_ID ] ) ? (string) $args[ self::BLOCK_ID ] : '';
	}

	/**
	 * @return string
	 */
	private function get_block_id(): string {
		return $this->block_id;
	}

	/**
	 * @return Post_Loader_Query_Response
	 */
	private function query(): Post_Loader_Query_Response {
		// Set the default parameters
		$request_params = [
			Post_Loader::PAGE_FIELD_NAME    => 1,
			Post_Loader::FILTERS_FIELD_NAME => [],
		];

		if ( $this->is_frontend_ajax_request() && $_POST ) {
			$filters = isset( $_POST[ Post_Loader::FILTERS_FIELD_NAME ] ) ? json_decode( stripslashes( $_POST[ Post_Loader::FILTERS_FIELD_NAME ] ), true ) : [];

			unset(
				$_POST[ Post_Loader::POST_FIELD_NAME ],
				$_POST[ Post_Loader::BLOCK_FIELD_NAME ],
				$_POST[ Post_Loader::NONCE_FIELD_NAME ],
				$_POST[ Post_Loader::FILTERS_FIELD_NAME ]
			);

			$request_params = [
				...$_POST,
				Post_Loader::FILTERS_FIELD_NAME => $filters,
			];
		}

		$query = $this->query_posts( $request_params );

		if ( is_wp_error( $query ) ) {
			return new Post_Loader_Query_Response( $query->get_error_message() );
		}

		if ( ! $query->have_posts() ) {
			return new Post_Loader_Query_Response( __( 'No posts found for the provided arguments.', 'tribe' ) );
		}

		$this->more_posts = $query->max_num_pages > $query->get( 'paged' );

		return new Post_Loader_Query_Response( 'Success', $query->posts );
	}

	/**
	 * @param array $container_classes
	 *
	 * @return void
	 */
	public function render_posts( array $container_classes = [] ): void {
		$post_loader_query_response = $this->query();

		if ( ! $post_loader_query_response->get_posts() ) {
			if ( is_admin() ) {
				echo Blocks_Utilities::format_admin_error_message( $post_loader_query_response->get_message() );
			}

			return;
		}

		$container_classes[] = self::CONTAINER_CLASS;

		wp_nonce_field( Post_Loader::NONCE_ACTION, Post_Loader::NONCE_FIELD_NAME, true, true );

		get_template_part( 'components/container/container', null, [
			Container_Controller::CLASSES => $container_classes,
			Container_Controller::ATTRS   => [
				'data-pid'   => get_the_ID(),
				'data-block' => $this->get_block_id(),
				'data-page'  => 1,
			],
			Container_Controller::CONTENT => $this->format_posts_to_html( $post_loader_query_response->get_posts() ),
		] );
	}

	/**
	 * @return bool
	 */
	private function is_frontend_ajax_request(): bool {
		return wp_doing_ajax() && ( ! str_contains( $_SERVER['HTTP_REFERER'] ?? '', '/wp-admin/' ) );
	}

	/**
	 * @return void
	 */
	public function render_ajax_posts(): void {
		if ( ! $this->is_frontend_ajax_request() ) {
			return;
		}

		$post_loader_query_response = $this->query();

		if ( ! $post_loader_query_response->get_posts() ) {
			wp_send_json_error( $post_loader_query_response->get_message() );
			wp_die();
		}

		$posts_html = $this->format_posts_to_html( $post_loader_query_response->get_posts() );

		wp_send_json_success( [
			'posts_html'     => $posts_html,
			'has_more_posts' => $this->more_posts,
		], 200 );

		wp_die();
	}

	/**
	 * @return bool
	 */
	public function has_more_posts(): bool {
		return $this->more_posts;
	}

	/**
	 * Every input changed inside the filter container will trigger the post loader
	 *
	 * @param string                    $filter_name
	 * @param bool                      $trigger_on_input_change
	 * @param string|Deferred_Component $html
	 *
	 * @return Deferred_Component
	 */
	public function generate_filter_container( string $filter_name, bool $trigger_on_input_change, string|Deferred_Component $html ): Deferred_Component {

		$classes = [
			self::FILTER_CLASS,
		];

		if ( ! $trigger_on_input_change ) {
			$classes[] = self::DISABLE_INPUT_TRIGGER_CLASS;
		}

		return defer_template_part( 'components/container/container', null, [
			Container_Controller::CLASSES => $classes,
			Container_Controller::ATTRS   => [ 'data-name' => $filter_name ],
			Container_Controller::CONTENT => $html,
		] );
	}

	/**
	 * @param string $button_text
	 * @param array  $classes
	 * @param string $id
	 * @param bool   $render_spinner
	 *
	 * @return void
	 */
	public function render_load_more_button( string $button_text, array $classes = [], string $id = '', bool $render_spinner = false ): void {
		get_template_part( 'components/button/button', null, [
			Button_Controller::CLASSES => [ ...$classes, self::LOAD_MORE_CLASS ],
			Button_Controller::ATTRS   => [],
			Button_Controller::TYPE    => 'button',
			Button_Controller::CONTENT => $button_text,
			Button_Controller::ID      => $id,
		] );

		if ( $render_spinner ) {
			$this->render_spinner();
		}
	}

	/**
	 * @return void
	 */
	public function render_spinner(): void {
		$class = self::SPINNER_CLASS;

		echo "<div class='$class'></div>";
	}

	public function get_reset_filters_trigger_class(): string {
		return self::RESET_FILTERS_CLASS;
	}

	public function get_apply_filters_trigger_class(): string {
		return self::APPLY_FILTERS_CLASS;
	}
}
