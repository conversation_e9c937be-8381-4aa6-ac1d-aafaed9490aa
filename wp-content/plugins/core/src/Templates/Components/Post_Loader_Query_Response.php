<?php

declare( strict_types=1 );

namespace Tribe\Project\Templates\Components;

use WP_Post;

class Post_Loader_Query_Response {
	private string     $message;
	private array|null $posts;

	/**
	 * @param string    $message
	 * @param WP_Post[] $posts
	 */
	public function __construct( string $message, array $posts = [] ) {
		$this->message = $message;
		$this->posts   = $posts ?: null;
	}

	/**
	 * @return string
	 */
	public function get_message(): string {
		return $this->message;
	}

	/**
	 * @return WP_Post[]|null
	 */
	public function get_posts(): ?array {
		return $this->posts;
	}
}
