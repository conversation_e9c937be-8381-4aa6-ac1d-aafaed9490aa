<?php

declare( strict_types=1 );

namespace Tribe\Project\Templates\Components;

use WP_Error;
use WP_Post;
use WP_Query;

interface Post_Loader_Interface {
	/**
	 * @param array $request_params
	 *
	 * @return WP_Query|WP_Error
	 */
	public function query_posts( array $request_params ): WP_Query|WP_Error;

	/**
	 * @param WP_Post[] $posts
	 *
	 * @return string
	 */
	public function format_posts_to_html( array $posts ): string;
}
