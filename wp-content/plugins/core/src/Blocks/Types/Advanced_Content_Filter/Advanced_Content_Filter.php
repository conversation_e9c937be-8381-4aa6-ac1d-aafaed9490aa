<?php
declare( strict_types=1 );

namespace Tribe\Project\Blocks\Types\Advanced_Content_Filter;

use Tribe\Libs\ACF\Field_Section;
use Tribe\Project\Blocks\Lib\Block_Config_Json;
use Tribe\Libs\ACF\Field;
use Tribe\Project\Post_Types\Download\Download;
use Tribe\Project\Post_Types\Media\Media;
use Tribe\Project\Post_Types\Member_Stream\Member_Stream;
use Tribe\Project\Post_Types\Page\Page;
use Tribe\Project\Post_Types\Post\Post;
use Tribe\Project\Post_Types\Tool_Post\Tool_Post;
use Tribe\Project\Taxonomies\Category\Category;
use Tribe\Project\Taxonomies\Download_Type\Download_Type;
use Tribe\Project\Taxonomies\Event_Type\Event_Type;
use Tribe\Project\Taxonomies\Format\Format;
use Tribe\Project\Taxonomies\Media_Type\Media_Type;
use Tribe\Project\Taxonomies\Post_Tag\Post_Tag;

class Advanced_Content_Filter extends Block_Config_Json {
	public const NAME = 'advancedcontentfilter';

	public const SECTION_CONTENT = 's-content';

	public const TITLE            = 'title';
	public const HEADING_TAG      = 'heading_tag';
	public const HEADING_TAG_H1   = 'h1';
	public const HEADING_TAG_H2   = 'h2';
	public const HEADING_TAG_H3   = 'h3';
	public const HEADING_TAG_H4   = 'h4';
	public const QUERY_TAXONOMIES = 'query_taxonomy_terms';
	public const QUERY_POST_TYPES = 'query_post_types';

	public const ALLOWED_POST_TYPES = [
		Page::NAME,
		Post::NAME,
		Tool_Post::NAME,
		Media::NAME,
		Member_Stream::NAME,
		Download::NAME,
	];

	public function add_block(): void {
		$this->set_block_json( self::NAME, [
			Block_Config_Json::COMPONENT_FILE_NAME => 'advanced_content_filter',
			Block_Config_Json::LOAD_STYLE          => true,
		] );
	}

	/**
	 * @return array
	 */
	private function get_post_types_labels(): array {
		if ( ! is_admin() ) {
			return [];
		}

		$array = [];

		foreach ( self::ALLOWED_POST_TYPES as $cpt ) {
			$obj = get_post_type_object( $cpt );
			if ( ! $obj ) {
				continue;
			}
			$array[ $cpt ] = esc_html( $obj->label );
		}

		return $array;
	}

	/**
	 * @return array
	 */
	private function get_taxonomies_for_post_types(): array {
		if ( ! is_admin() ) {
			return [];
		}

		global $wp_taxonomies;

		$taxonomy_options[ Category::NAME ]      = __( 'Topics', 'tribe' );
		$taxonomy_options[ Post_Tag::NAME ]      = $wp_taxonomies[ Post_Tag::NAME ]->label;
		$taxonomy_options[ Media_Type::NAME ]    = $wp_taxonomies[ Media_Type::NAME ]->label;
		$taxonomy_options[ Download_Type::NAME ] = $wp_taxonomies[ Download_Type::NAME ]->label;
		$taxonomy_options[ Event_Type::NAME ]    = $wp_taxonomies[ Event_Type::NAME ]->label;
		$taxonomy_options[ Format::NAME ]        = $wp_taxonomies[ Format::NAME ]->label;

		return $taxonomy_options;
	}


	protected function add_fields(): void {
		//==========================================
		// Content Fields
		//==========================================
		$this->add_section( new Field_Section( self::SECTION_CONTENT, __( 'Content', 'tribe' ), 'accordion' ) )
			->add_field( new Field( self::NAME . '_' . self::TITLE, [
					'label' => __( 'Title', 'tribe' ),
					'name'  => self::TITLE,
					'type'  => 'text',
				] )
			)->add_field( new Field( self::NAME . '_' . self::HEADING_TAG, [
					'label'         => __( 'Heading Tag', 'tribe' ),
					'name'          => self::HEADING_TAG,
					'type'          => 'select',
					'ui'            => 1,
					'multiple'      => 0,
					'allow_null'    => 0,
					'choices'       => [
						self::HEADING_TAG_H1 => self::HEADING_TAG_H1,
						self::HEADING_TAG_H2 => self::HEADING_TAG_H2,
						self::HEADING_TAG_H3 => self::HEADING_TAG_H3,
						self::HEADING_TAG_H4 => self::HEADING_TAG_H4,
					],
					'default_value' => self::HEADING_TAG_H2,
				] )
			)->add_field(
				new Field( self::NAME . '_' . self::QUERY_POST_TYPES, [
					'type'     => 'select',
					'label'    => __( 'Post Types', 'tribe' ),
					'multiple' => true,
					'required' => true,
					'ui'       => true,
					'name'     => self::QUERY_POST_TYPES,
					'choices'  => $this->get_post_types_labels(),
				] )
			)->add_field(
				new Field( self::NAME . '_' . self::QUERY_TAXONOMIES, [
					'type'          => 'select',
					'multiple'      => true,
					'label'         => __( 'Filter by Taxonomies', 'tribe' ),
					'ui'            => true,
					'name'          => self::QUERY_TAXONOMIES,
					'required'      => true,
					'return_format' => 'value',
					'max'           => 3,
					'choices'       => $this->get_taxonomies_for_post_types(),
				] )
			);
	}

}
