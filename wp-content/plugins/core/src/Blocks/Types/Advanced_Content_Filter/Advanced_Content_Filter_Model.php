<?php
declare( strict_types=1 );

namespace Tribe\Project\Blocks\Types\Advanced_Content_Filter;

use Tribe\Project\Blocks\Types\Base_Model;
use Tribe\Project\Templates\Components\blocks\advanced_content_filter\Advanced_Content_Filter_Block_Controller;

class Advanced_Content_Filter_Model extends Base_Model {

	/**
	 * @return array
	 */
	public function get_data(): array {
		return [
			Advanced_Content_Filter_Block_Controller::BLOCK_ID         => $this->get_block_id(),
			Advanced_Content_Filter_Block_Controller::ATTRS            => $this->get_attrs(),
			Advanced_Content_Filter_Block_Controller::CLASSES          => $this->get_classes(),
			Advanced_Content_Filter_Block_Controller::TITLE            => $this->get( Advanced_Content_Filter::TITLE, '' ),
			Advanced_Content_Filter_Block_Controller::HEADING_TAG      => $this->get( Advanced_Content_Filter::HEADING_TAG, Advanced_Content_Filter::HEADING_TAG_H2 ),
			Advanced_Content_Filter_Block_Controller::QUERY_POST_TYPES => $this->get( Advanced_Content_Filter::QUERY_POST_TYPES, [] ),
			Advanced_Content_Filter_Block_Controller::QUERY_TAXONOMIES => $this->get( Advanced_Content_Filter::QUERY_TAXONOMIES, [] ),
		];
	}
}
