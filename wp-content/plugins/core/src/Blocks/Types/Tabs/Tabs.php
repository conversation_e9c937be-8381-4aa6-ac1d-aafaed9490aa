<?php
declare( strict_types=1 );

namespace Tribe\Project\Blocks\Types\Tabs;

use Tribe\Libs\ACF\Field;
use Tribe\Libs\ACF\Field_Section;
use Tribe\Libs\ACF\Repeater;
use Tribe\Project\Blocks\Lib\Block_Config_Json;

class Tabs extends Block_Config_Json {
	public const NAME = 'tabs';

	public const SECTION_CONTENT = 's-content';
	public const TITLE           = 'title';
	public const DESCRIPTION     = 'description';
	public const CTA             = 'cta';

	public const TABS      = 'tabs';
	public const TAB_LABEL = 'tab_label';

	public const SECTION_SETTINGS  = 's-settings';
	public const LAYOUT            = 'layout';
	public const LAYOUT_HORIZONTAL = 'horizontal';
	public const LAYOUT_VERTICAL   = 'vertical';

	/**
	 * Register the block
	 */
	public function add_block(): void {
		$this->set_block_json( self::NAME, [
			Block_Config_Json::LOAD_STYLE => true,
		] );
	}

	/**
	 * Register Fields for block
	 */
	public function add_fields(): void {
		//==========================================
		// Content Fields
		//==========================================
		$this->add_section( new Field_Section( self::SECTION_CONTENT, __( 'Content', 'tribe' ), 'accordion' ) )
			->add_field( new Field( self::NAME . '_' . self::TITLE, [
					'label' => __( 'Title', 'tribe' ),
					'name'  => self::TITLE,
					'type'  => 'text',
				] )
			)->add_field( new Field( self::NAME . '_' . self::DESCRIPTION, [
					'label'        => __( 'Description', 'tribe' ),
					'name'         => self::DESCRIPTION,
					'type'         => 'wysiwyg',
					'toolbar'      => 'basic',
					'media_upload' => 0,
				] )
			)->add_field( new Field( self::NAME . '_' . self::CTA, [
					'label' => __( 'Call to Action', 'tribe' ),
					'name'  => self::CTA,
					'type'  => 'link',
				] )
			)->add_field( $this->get_tab_section() );

		//==========================================
		// Settings Fields
		//==========================================
		$this->add_section( new Field_Section( self::SECTION_SETTINGS, __( 'Settings', 'tribe' ), 'accordion' ) )
			->add_field( new Field( self::NAME . '_' . self::LAYOUT, [
				'type'            => 'image_select',
				'name'            => self::LAYOUT,
				'choices'         => [
					self::LAYOUT_VERTICAL   => __( 'Vertical', 'tribe' ),
					self::LAYOUT_HORIZONTAL => __( 'Horizontal', 'tribe' ),
				],
				'default_value'   => self::LAYOUT_HORIZONTAL,
				'multiple'        => 0,
				'image_path'      => sprintf(
					'%sassets/img/admin/blocks/%s/',
					trailingslashit( get_template_directory_uri() ),
					self::NAME
				),
				'image_extension' => 'svg',
			] ) );
	}

	/**
	 * @return Repeater
	 */
	protected function get_tab_section(): Repeater {
		$group = new Repeater( self::NAME . '_' . self::TABS );
		$group->set_attributes( [
			'label'        => __( 'Tab Section', 'tribe' ),
			'name'         => self::TABS,
			'layout'       => 'block',
			'min'          => 0,
			'max'          => 10,
			'button_label' => __( 'Add Tab', 'tribe' ),
		] );
		$header = new Field( self::TAB_LABEL, [
			'label' => __( 'Tab Label', 'tribe' ),
			'name'  => self::TAB_LABEL,
			'type'  => 'text',
		] );

		$group->add_field( $header );

		return $group;
	}

}
