<?php
declare( strict_types=1 );

namespace Tribe\Project\Blocks\Types\Content_Chapter;

use Tribe\Project\Blocks\Types\Base_Model;
use Tribe\Project\Templates\Components\blocks\content_chapter\Content_Chapter_Block_Controller;

class Content_Chapter_Model extends Base_Model {
	/**
	 * @return array
	 */
	public function get_data(): array {
		return [
			Content_Chapter_Block_Controller::ATTRS                  => $this->get_attrs(),
			Content_Chapter_Block_Controller::CLASSES                => $this->get_classes(),
			Content_Chapter_Block_Controller::CHAPTER_TITLE          => $this->get( Content_Chapter::CHAPTER_TITLE ),
			Content_Chapter_Block_Controller::HEADER_DARK_BACKGROUND => $this->get( Content_Chapter::HEADER_DARK_BACKGROUND ),
			Content_Chapter_Block_Controller::MEDIA_TYPE             => $this->get( Content_Chapter::MEDIA_TYPE, Content_Chapter::IMAGE ),
			Content_Chapter_Block_Controller::HEADER_IMAGE           => $this->get( Content_Chapter::HEADER_IMAGE, 0 ),
			Content_Chapter_Block_Controller::LOTTIE_DATA            => $this->get( Content_Chapter::LOTTIE ),
			Content_Chapter_Block_Controller::LOTTIE_FILE_HEIGHT     => $this->get( Content_Chapter::LOTTIE_FILE_HEIGHT ),
			Content_Chapter_Block_Controller::LOTTIE_FILE_WIDTH      => $this->get( Content_Chapter::LOTTIE_FILE_WIDTH ),
		];
	}
}
