<?php
declare( strict_types=1 );

namespace Tribe\Project\Blocks\Types\Content_Chapter;

use Tribe\Libs\ACF\Field;
use Tribe\Libs\ACF\Field_Section;
use Tribe\Project\Blocks\Lib\Block_Config_Json;
use Tribe\Project\Templates\Components\Traits\With_Lottie_Fields;

class Content_Chapter extends Block_Config_Json {
	use With_Lottie_Fields;

	public const NAME = 'contentchapter';

	public const SECTION_CONTENT = 's-content';

	public const HEADER_IMAGE                   = 'header_image';
	public const CHAPTER_TITLE                  = 'chapter_title';
	public const CONTENT                        = 'content';
	public const HEADER_DARK_BACKGROUND         = 'header_dark_background';
	public const HEADER_DARK_BACKGROUND_DEFAULT = true;

	public const MEDIA_TYPE         = 'media_type';
	public const IMAGE              = 'image';
	public const LOTTIE_FILE_HEIGHT = 'lottie_file_height';
	public const LOTTIE_FILE_WIDTH  = 'lottie_file_width';

	public function add_block(): void {
		$this->set_block_json( self::NAME, [
			Block_Config_Json::COMPONENT_FILE_NAME => 'content_chapter',
			Block_Config_Json::LOAD_STYLE          => true,
		] );
	}

	protected function add_fields(): void {
		$this->add_section( new Field_Section( self::SECTION_CONTENT, __( 'Content', 'tribe' ), 'accordion' ) )
			->add_field( new Field( self::NAME . '_' . self::MEDIA_TYPE, [
					'label'         => __( 'Header Background Media Type', 'tribe' ),
					'name'          => self::MEDIA_TYPE,
					'type'          => 'radio',
					'choices'       => [
						self::IMAGE  => __( 'Image', 'tribe' ),
						self::LOTTIE => __( 'Lottie Animation', 'tribe' ),
					],
					'default_value' => [
						self::IMAGE,
					],
				] )
			)->add_field( new Field( self::NAME . '_' . self::HEADER_IMAGE, [
					'label'             => __( 'Image', 'tribe' ),
					'name'              => self::HEADER_IMAGE,
					'type'              => 'image',
					'return_format'     => 'id',
					'instructions'      => __( 'Recommended image size: 1920px by 1080px.', 'tribe' ),
					'conditional_logic' => [
						[
							[
								'field'    => 'field_' . self::NAME . '_' . self::MEDIA_TYPE,
								'operator' => '==',
								'value'    => self::IMAGE,
							],
						],
					],
				] )
			)->add_field(
				$this->get_lottie_fields( self::NAME . '_' . self::MEDIA_TYPE, self::LOTTIE, true )
			)->add_field( new Field( self::NAME . '_' . self::LOTTIE_FILE_WIDTH, [
				'label'             => __( 'Lottie Dimensions Width', 'tribe' ),
				'instructions'      => __( 'Include px (example <code>100px</code>)', 'tribe' ),
				'name'              => self::LOTTIE_FILE_WIDTH,
				'type'              => 'text',
				'required'          => 1,
				'conditional_logic' => [
					[
						[
							'field'    => 'field_' . self::NAME . '_' . self::MEDIA_TYPE,
							'operator' => '==',
							'value'    => self::LOTTIE,
						],
					],
				],
			] ) )->add_field( new Field( self::NAME . '_' . self::LOTTIE_FILE_HEIGHT, [
				'label'             => __( 'Lottie Dimensions Height', 'tribe' ),
				'instructions'      => __( 'Include px (example <code>100px</code>)', 'tribe' ),
				'name'              => self::LOTTIE_FILE_HEIGHT,
				'type'              => 'text',
				'required'          => 1,
				'conditional_logic' => [
					[
						[
							'field'    => 'field_' . self::NAME . '_' . self::MEDIA_TYPE,
							'operator' => '==',
							'value'    => self::LOTTIE,
						],
					],
				],
			] ) )->add_field( new Field( self::NAME . '_' . self::HEADER_DARK_BACKGROUND, [
					'label'         => __( 'Chapter header Has a Dark Background?', 'tribe' ),
					'name'          => self::HEADER_DARK_BACKGROUND,
					'instructions'  => __( 'Is the background image dark?.', 'tribe' ),
					'type'          => 'true_false',
					'default_value' => self::HEADER_DARK_BACKGROUND_DEFAULT,
					'ui'            => 1,
				] )
			)->add_field( new Field( self::NAME . '_' . self::CHAPTER_TITLE, [
					'label' => __( 'Chapter Title', 'tribe' ),
					'name'  => self::CHAPTER_TITLE,
					'type'  => 'text',
				] )
			);
	}

}
