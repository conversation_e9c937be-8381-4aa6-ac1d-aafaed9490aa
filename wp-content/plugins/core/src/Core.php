<?php

namespace Tribe\Project;

use DI\ContainerBuilder;
use Psr\Container\ContainerInterface;
use Tribe\Libs\Routes\Route_Definer;
use Tribe\Libs\Routes\Route_Subscriber;
use Tribe\Project\AB_Tools\AB_Tools_Subscriber;
use Tribe\Project\Admin\Admin_Subscriber;
use Tribe\Project\Admin\Content_Shift_Subscriber;
use Tribe\Project\Analytics\Analytics_Subscriber;
use Tribe\Project\Assets\Assets_Subscriber;
use Tribe\Project\Blocks\Blocks_Definer;
use Tribe\Project\Blocks\Blocks_Subscriber;
use Tribe\Project\Cache\Cache_Subscriber;
use Tribe\Project\CLI\CLI_Definer;
use Tribe\Project\CLI\CLI_Subscriber;
use Tribe\Project\Components\Gravity_Form\Gravity_Form_Subscriber;
use Tribe\Project\Components\Content_Loader\Content_Loader_Subscriber;
use Tribe\Project\Components\Load_More_Posts\Load_More_Posts_Subscriber;
use Tribe\Project\Components\Taxonomy_Filter\Taxonomy_Filter_Subscriber;
use Tribe\Project\Components\Modal\Modal_Subscriber;
use Tribe\Project\Components\Post_Loader\Post_Loader_Subscriber;
use Tribe\Project\Email\Email_Subscriber;
use Tribe\Project\Feed\Feed_Subscriber;
use Tribe\Project\Integrations\ACF\ACF_Subscriber;
use Tribe\Project\Integrations\Adsanity\Adsanity_Subscriber;
use Tribe\Project\Integrations\Adsanity\Tribe_Adsanity_Definer;
use Tribe\Project\Integrations\ExchangeRateApi\ExchangeRateApi_Subscriber;
use Tribe\Project\Integrations\Google_Tag_Manager\Google_Tag_Manager_Subscriber;
use Tribe\Project\Integrations\Gravity_Forms\Gravity_Forms_Subscriber;
use Tribe\Project\Integrations\Gravity_Perks\Gravity_Perks_Subscriber;
use Tribe\Project\Integrations\GSAP\GSAP_Subscriber;
use Tribe\Project\Integrations\Hubspot\Hubspot_Subscriber;
use Tribe\Project\Integrations\Jetpack\Jetpack_Subscriber;
use Tribe\Project\Integrations\PolyLang_Pro\PolyLang_Pro_Subscriber;
use Tribe\Project\Integrations\Scripts\Scripts_Subscriber;
use Tribe\Project\Integrations\Sentry\Sentry_Subscriber;
use Tribe\Project\Integrations\Slack\Slack_Subscriber;
use Tribe\Project\Integrations\SSL\Deep_Links\SSL_Deep_Links_Subscriber;
use Tribe\Project\Integrations\SSL\SSL_Subscriber;
use Tribe\Project\Integrations\Yoast_SEO\Yoast_SEO_Subscriber;
use Tribe\Project\Integrations\Memberpress\Memberpress_Subscriber;
use Tribe\Project\Integrations\ZestyHorizon\ZestyHorizon_Subscriber;
use Tribe\Project\Nav_Menus\Nav_Menus_Definer;
use Tribe\Project\Nav_Menus\Nav_Menus_Subscriber;
use Tribe\Project\Object_Meta\Object_Meta_Definer;
use Tribe\Project\Permalinks\Permalinks_Subscriber;
use Tribe\Project\Post_Types\Download\Subscriber as Download_Subscriber;
use Tribe\Project\Post_Types\Media\Subscriber as Media_Post_Type_Subscriber;
use Tribe\Project\Post_Types\Member_Stream\Subscriber as Member_Stream_Subscriber;
use Tribe\Project\Post_Types\Modal\Subscriber as Modal_CPT_Subscriber;
use Tribe\Project\Post_Types\Service_Post\Subscriber as Service_Post_Subscriber;
use Tribe\Project\Post_Types\Tool_Data\Subscriber as Tool_Data_Subscriber;
use Tribe\Project\Post_Types\Tool_Post\Subscriber as Tool_Post_Subscriber;
use Tribe\Project\Post_Types\Partner_Spotlight\Subscriber as Partner_Spotlight_Subscriber;
use Tribe\Project\Post_Types\Post\Subscriber as Post_Subscriber;
use Tribe\Project\Rewrite_Rules\Rewrite_Rules_Subscriber;
use Tribe\Project\Routes\Parse_Query_Subscriber;
use Tribe\Project\Routes\Routes_Definer;
use Tribe\Project\Routes\Routes_Subscriber;
use Tribe\Project\Security\Security_Subscriber;
use Tribe\Project\Settings\Settings_Definer;
use Tribe\Project\Shortcodes\Shortcodes_Subscriber;
use Tribe\Project\Taxonomies\Business_Size\Subscriber as Business_Size_Subscriber;
use Tribe\Project\Taxonomies\Category\Subscriber as Category_Subscriber;
use Tribe\Project\Taxonomies\Download_Type\Subscriber as Download_Type_Subscriber;
use Tribe\Project\Taxonomies\Event_Type\Subscriber as Event_Type_Subscriber;
use Tribe\Project\Taxonomies\Format\Subscriber as Format_Subscriber;
use Tribe\Project\Taxonomies\Industry\Subscriber as Industry_Subscriber;
use Tribe\Project\Taxonomies\Media_Type\Subscriber as Media_Type_Subscriber;
use Tribe\Project\Taxonomies\Post_State\Subscriber as Post_State_Subscriber;
use Tribe\Project\Taxonomies\Product_Funnel\Subscriber as Product_Funnel_Subscriber;
use Tribe\Project\Taxonomies\Project\Subscriber as Project_Subscriber;
use Tribe\Project\Taxonomies\Service_Format\Subscriber as Service_Format_Subscriber;
use Tribe\Project\Taxonomies\Tool_Format\Subscriber as Tool_Format_Subscriber;
use Tribe\Project\Template_Tags\Template_Tags_Subscriber;
use Tribe\Project\Theme_Customizer\Theme_Customizer_Subscriber;
use Tribe\Project\Theme\Theme_Definer;
use Tribe\Project\Theme\Theme_Subscriber;
use Tribe\Project\Users\Users_Subscriber;

class Core {
	public const PLUGIN_FILE = 'plugin.file';

	/**
	 * @var self
	 */
	private static $instance;

	/**
	 * @var ContainerInterface
	 */
	private $container;

	/**
	 * @var string[] Names of classes implementing Definer_Interface
	 */
	private $definers = [
		Blocks_Definer::class,
		CLI_Definer::class,
		Nav_Menus_Definer::class,
		Object_Meta_Definer::class,
		Settings_Definer::class,
		Theme_Definer::class,
		Tribe_Adsanity_Definer::class,
		Routes_Definer::class,
	];

	/**
	 * @var string[] Names of classes extending Abstract_Subscriber
	 */
	private $subscribers = [
		AB_Tools_Subscriber::class,
		ACF_Subscriber::class,
		Adsanity_Subscriber::class,
		Admin_Subscriber::class,
		Content_Shift_Subscriber::class,
		Analytics_Subscriber::class,
		Assets_Subscriber::class,
		Blocks_Subscriber::class,
		Cache_Subscriber::class,
		CLI_Subscriber::class,
		Gravity_Form_Subscriber::class,
		Content_Loader_Subscriber::class,
		Load_More_Posts_Subscriber::class,
		Post_Loader_Subscriber::class,
		Taxonomy_Filter_Subscriber::class,
		Modal_Subscriber::class,
		Category_Subscriber::class,
		Download_Subscriber::class,
		Download_Type_Subscriber::class,
		Email_Subscriber::class,
		Event_Type_Subscriber::class,
		ExchangeRateApi_Subscriber::class,
		Feed_Subscriber::class,
		Format_Subscriber::class,
		Google_Tag_Manager_Subscriber::class,
		Gravity_Forms_Subscriber::class,
		Gravity_Perks_Subscriber::class,
		GSAP_Subscriber::class,
		Hubspot_Subscriber::class,
		Jetpack_Subscriber::class,
		Media_Post_Type_Subscriber::class,
		Media_Type_Subscriber::class,
		Modal_CPT_Subscriber::class,
		Memberpress_Subscriber::class,
		Member_Stream_Subscriber::class,
		Nav_Menus_Subscriber::class,
		Partner_Spotlight_Subscriber::class,
		Parse_Query_Subscriber::class,
		Permalinks_Subscriber::class,
		PolyLang_Pro_Subscriber::class,
		Post_Subscriber::class,
		Post_State_Subscriber::class,
		Rewrite_Rules_Subscriber::class,
		Routes_Subscriber::class,
		Scripts_Subscriber::class,
		Security_Subscriber::class,
		Sentry_Subscriber::class,
		Service_Post_Subscriber::class,
		Service_Format_Subscriber::class,
		Shortcodes_Subscriber::class,
		Slack_Subscriber::class,
		SSL_Deep_Links_Subscriber::class,
		SSL_Subscriber::class,
		Template_Tags_Subscriber::class,
		Theme_Customizer_Subscriber::class,
		Theme_Subscriber::class,
		Tool_Data_Subscriber::class,
		Tool_Format_Subscriber::class,
		Tool_Post_Subscriber::class,
		Users_Subscriber::class,
		Yoast_SEO_Subscriber::class,
		Product_Funnel_Subscriber::class,
		Project_Subscriber::class,
		Industry_Subscriber::class,
		Business_Size_Subscriber::class,
		ZestyHorizon_Subscriber::class,
	];

	/**
	 * @var array Names of classes from Tribe Libs implementing Definer_Interface
	 */
	private $lib_definers = [
		'\Tribe\Libs\Assets\Assets_Definer',
		'\Tribe\Libs\Blog_Copier\Blog_Copier_Definer',
		'\Tribe\Libs\Cache\Cache_Definer',
		'\Tribe\Libs\CLI\CLI_Definer',
		'\Tribe\Libs\Generators\Generator_Definer',
		'\Tribe\Libs\Media\Media_Definer',
		'\Tribe\Libs\Object_Meta\Object_Meta_Definer',
		'\Tribe\Libs\Queues\Queues_Definer',
		'\Tribe\Libs\Queues_Mysql\Mysql_Backend_Definer',
		'\Tribe\Libs\Required_Page\Required_Page_Definer',
		'\Tribe\Libs\Settings\Settings_Definer',
		'\Tribe\Libs\Whoops\Whoops_Definer',
		Route_Definer::class,
	];

	/**
	 * @var array Names of classes from Tribe Libs extending Abstract_Subscriber
	 */
	private $lib_subscribers = [
		'\Tribe\Libs\Blog_Copier\Blog_Copier_Subscriber',
		'\Tribe\Libs\Cache\Cache_Subscriber',
		'\Tribe\Libs\CLI\CLI_Subscriber',
		'\Tribe\Libs\Media\Media_Subscriber',
		'\Tribe\Libs\Object_Meta\Object_Meta_Subscriber',
		'\Tribe\Libs\Queues\Queues_Subscriber',
		'\Tribe\Libs\Queues_Mysql\Mysql_Backend_Subscriber',
		'\Tribe\Libs\Required_Page\Required_Page_Subscriber',
		'\Tribe\Libs\Settings\Settings_Subscriber',
		'\Tribe\Libs\Whoops\Whoops_Subscriber',
		Route_Subscriber::class,
	];


	public function init( string $plugin_path ) {
		$this->init_container( $plugin_path );
	}

	private function init_container( string $plugin_path ): void {
		// combine definers/subscribers from the project and libs
		$definers    = array_merge( array_filter( $this->lib_definers, 'class_exists' ), $this->definers );
		$subscribers = array_merge( array_filter( $this->lib_subscribers, 'class_exists' ), $this->subscribers );

		/**
		 * Filter the list of definers that power the plugin
		 *
		 * @param string[] $definers The class names of definers that will be instantiated
		 */
		$definers = apply_filters( 'tribe/project/definers', $definers );

		/**
		 * Filter the list subscribers that power the plugin
		 *
		 * @param string[] $subscribers The class names of subscribers that will be instantiated
		 */
		$subscribers = apply_filters( 'tribe/project/subscribers', $subscribers );

		$builder = new ContainerBuilder();
		$builder->useAutowiring( true );
		$builder->useAnnotations( false );
		$builder->addDefinitions( [ self::PLUGIN_FILE => $plugin_path ] );
		$builder->addDefinitions( ... array_map( function ( $classname ) {
			return ( new $classname() )->define();
		}, $definers ) );

		$this->container = $builder->build();

		foreach ( $subscribers as $subscriber_class ) {
			( new $subscriber_class( $this->container ) )->register();
		}
	}

	public function container(): ContainerInterface {
		return $this->container;
	}

	/**
	 * @return self
	 */
	public static function instance() {
		if ( ! isset( self::$instance ) ) {
			self::$instance = new self();
		}

		return self::$instance;
	}

}
