{"apiVersion": 2, "name": "acf/advancedcontentfilter", "title": "Advanced Content Filter", "description": "Advanced Content Filter block", "icon": "<svg width=\"20\" height=\"20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path fill=\"#fff\" stroke=\"#000\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M.5.5h19v19H.5z\"/><path d=\"M13.6 12.7H6.4v3.6h7.2v-3.6zM3.7 4.6h12.6v1.8H3.7zM6.4 7.3h7.2v1.8H6.4z\" fill=\"#000\"/><path d=\"M10.517 14.305H8.732v.17h1.785v-.17zM10.517 14.393v-.567l.298.284.299.283-.299.283-.298.283v-.566z\" fill=\"#fff\"/></svg>", "category": "common", "keywords": ["advanced_content_filter", "display"], "supports": {"align": false, "anchor": true}, "attributes": {"uniqid": {"type": "string", "default": ""}}, "example": {"attributes": {"mode": "preview", "data": {"preview_image": "<img alt=\"Advanced Content Filter Block Preview\" src=\"/wp-content/themes/core/assets/img/admin/blocks/block-previews/advanced_content_filter_block.png\">"}}}, "style": "style-block-advancedcontentfilter", "acf": {"mode": "preview", "renderTemplate": "advancedcontentfilter.php"}}