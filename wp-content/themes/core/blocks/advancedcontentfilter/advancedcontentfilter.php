<?php

declare( strict_types=1 );

use Tribe\Project\Blocks\Types\Advanced_Content_Filter\Advanced_Content_Filter_Model;

/**
 * @var array $block Arguments passed to the template
 */
// phpcs:ignore VariableAnalysis.CodeAnalysis.VariableAnalysis.UndefinedVariable
$model = new Advanced_Content_Filter_Model( $block );

$preview_image = $model->get( 'preview_image', '' );

/**
 * Render a block preview template when adding a new block, else render the full block template
 */
if ( ! empty( $preview_image ) ) {
	get_template_part( 'blocks/preview/preview', null, [ 'preview_image' => $preview_image ] );
} else {
	get_template_part( 'components/blocks/advanced_content_filter/advanced_content_filter', null, $model->get_data() );
}
