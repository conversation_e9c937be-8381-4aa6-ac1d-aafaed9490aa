<?php
declare( strict_types=1 );

use Tribe\Project\Object_Meta\Post_Settings_Meta;
use Tribe\Project\Templates\Components\related_posts\Related_Posts_Controller;
use \Tribe\Project\Templates\Components\routes\single\Single_Editorial_Controller;
use Tribe\Project\Object_Meta\Post_Template_Editorial_Meta;

$c = Single_Editorial_Controller::factory();

$c->render_header();

$conditional_styles = '';

if ( $c->get_header_has_dark_background() ) {
	$conditional_styles .= ' has-dark-background-color';
}
?>

<?php if ( $c->hide_navigation() ) : ?>
	<header class="site-header-editorial <?php echo $conditional_styles; ?>">
		<?php if ( $c->get_logo() ) : ?>
			<div class="site-header-editorial__logo-wrap">
				<?php echo $c->get_logo(); ?>
			</div>
		<?php endif; ?>
		<div class="site-header-editorial__icons-wrap">
			<?php if ( $c->get_newsletter_link() ) : ?>
				<a href="<?php echo $c->get_newsletter_link(); ?>" aria-label="Newsletter"
					class="site-header-editorial__icons-link">
					<i class="icon icon-mail icon-mail--newsletter"></i>
				</a>
			<?php endif; ?>
			<button class="site-header-editorial__icons-link" aria-label="Open the menu" rel="nofollow"
				data-js="open-menu-trigger">
				<i class="icon icon-hamburger"></i>
			</button>
		</div>
	</header>
<?php endif; ?>

	<main id="main-content" class="template--editorial <?php echo $c->hide_navigation() ? 'hide-nav' : ''; ?>">

		<article class="item-single item-single--editorial" data-js="item-single">

			<div class="item-single__subheader-wrapper <?php echo $conditional_styles; ?>">

				<?php if ( $c->get_media_type() === Post_Template_Editorial_Meta::IMAGE ) : ?>
					<div class="item-single__subheader-background"
						<?php if ( $c->get_header_background_image() ) : ?>
							style="background-image: url('<?php echo $c->get_header_background_image(); ?>');"
						<?php endif; ?>
					></div>
				<?php else : ?>
					<?php echo $c->get_header_lottie_animation() ?>
				<?php endif; ?>

				<div class="l-container">

					<div class="item-single__subheader-container">

						<div class="item-single__post-meta">

							<div class="item-single__category-meta">
								<?php if ( $c->get_primary_category_link_args() ) { ?>
									<div class="item-single__category">
										<a href="<?php echo $c->get_primary_category_link_args()->link; ?>" class="item-single__category-link">
											<?php echo $c->get_primary_category_link_args()->label; ?>
										</a>
									</div>
								<?php } ?>
							</div>

							<?php get_template_part( 'components/text/text', null, $c->get_title_args() ); ?>

							<?php if ( ! $c->hide_quick_summary() && ! empty( $c->get_excerpt() ) ) { ?>
								<div class="item-single__excerpt">
									<p class="item-single__excerpt-content"><?php echo $c->get_excerpt(); ?></p>
								</div>
							<?php } ?>

							<div class="item-single__cta">
								<?php echo $c->get_header_cta(); ?>
							</div>

							<?php if ( ! $c->hide_author() || ! $c->hide_date() ) : ?>
								<?php get_template_part( 'components/post_info/post_info', null, [
										'show_author' 		=> ! $c->hide_author(),
										'show_avatar' 		=> true,
										'show_date'   		=> ! $c->hide_date(),
										'prefix'      		=> 'By',
										'author_args' 		=> $c->get_author_args( [
												'size'             => 64,
												'show_description' => false,
										] ),
										'multiple_authors'	=> $c->get_multiple_authors(),
								] ); ?>
							<?php endif; ?>

							<?php if ( $c->get_disclaimer() ) { ?>
								<div class="item-single__disclaimer">
									<?php echo $c->get_disclaimer(); ?>
								</div>
							<?php } ?>
						</div>
					</div>
				</div>
			</div>

			<div class="item-single__content s-sink t-sink l-sink" id="content">
				<?php
				$c->render_ad( Post_Settings_Meta::AD_GROUP_POST_HEADER, 'header' );

				$blocks               = $c->convert_enclosing_shortcode_blocks_to_html_blocks( parse_blocks( get_the_content() ) );
				$counting_paragraphs  = 0;

				foreach ( $blocks as $key => $block ) {
					$is_paywall_paragraph     = $counting_paragraphs === $c->get_paywall_paragraph_limit();
					$is_content_chapter_block = array_key_exists( 'blockName', $block ) && 'acf/contentchapter' === $block['blockName'];

					if ( ( $is_content_chapter_block || $is_paywall_paragraph ) && $paywall_content_start = $c->render_paywall_content_start() ) {
						echo $paywall_content_start;
					}

					if ( array_key_exists( 'blockName', $block ) && 'core/group' === $block['blockName'] &&
						array_key_exists( 'innerBlocks', $block ) && ! empty( $block['innerBlocks'] ) ) {
						echo apply_filters( 'the_content', render_block( $block ) );
					} elseif ( array_key_exists( 'blockName', $block ) && 'core/embed' === $block['blockName'] ) {
						echo apply_filters( 'the_content', render_block( $block ) );
					} else {
						echo do_shortcode( render_block( $block ) );
					}

					// Count every paragraph. Used in Regwalls.
					if ( array_key_exists( 'blockName', $block ) && 'core/paragraph' === $block['blockName'] ) {
						$counting_paragraphs ++;
					}
				}

				if ( $paywall_content_end = $c->render_paywall_content_end() ) {
					echo $paywall_content_end;
				}

				$c->render_ad( Post_Settings_Meta::AD_GROUP_FOOTER, 'footer' );
				?>
			</div>

			<?php if ( ! $c->hide_author() ) : ?>
				<footer class="item-single__footer l-sink 11">
					<?php get_template_part( 'components/author/author', null, $c->get_author_args( [
							'size'             => 120,
							'show_description' => true,
					] ) ); ?>

					<?php echo $c->get_reviewer_as_coauthor(); ?>
					<?php echo $c->get_multiauthor(); ?>
				</footer>
			<?php endif; ?>
		</article>

		<?php if ( ! $c->hide_bottom_recommendations() && $related_posts = Related_Posts_Controller::get_primary_category_posts() ) { ?>
			<?php get_template_part( 'components/related_posts/related_posts', null, $related_posts ); ?>
		<?php } ?>
	</main>

<?php
$c->render_sidebar();
$c->render_footer();
