/**
 * @module Member Stream Event Time
 * @description Controls the users local time for the Member Stream upcoming event
 */

import * as tools from 'utils/tools';
import { convertToLocalTimezone, timezoneFormat, timezoneLocale } from 'utils/data/local-timezone';

const instances = {
	eventDate: tools.getNodes( '.item-single__date', true, document, true )[ 0 ],
	eventDay: tools.getNodes( '.item-single__date-day', true, document, true )[ 0 ],
	eventTime: tools.getNodes( '.item-single__date-time', true, document, true )[ 0 ],
};

/**
 * @function updateUI
 * @description
 */

const updateUI = () => {
	const { eventDate, eventTime, eventDay } = instances;

	if ( ! eventTime || ! eventDay || ! eventDate ) {
		return;
	}

	eventTime.innerHTML = `<i class="icon icon-clock"></i>${ convertToLocalTimezone( eventDate.dataset.date, timezoneFormat.TIME_AM_PM, timezoneLocale.EN ) }`;
	eventDay.innerHTML = `<i class="icon icon-calendar"></i>${ convertToLocalTimezone( eventDate.dataset.date, timezoneFormat.WEEKDAY_MONTH_DAY, timezoneLocale.EN ) }`;
};

const init = () => {
	updateUI();

	console.info( 'SquareOne Theme: Initialized Member Stream Single' );
};

export default init;
