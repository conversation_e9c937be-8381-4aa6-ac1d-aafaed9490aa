/* -----------------------------------------------------------------------------
 *
 * Component: Table Of Contents
 *
 * ----------------------------------------------------------------------------- */

.c-table-of-contents {
	position: relative;
	width: 100%;
	max-height: var(--toc-max-height);
	border: 1px solid var(--color-neutral-20);
	border-radius: var(--border-radius-highlights-small);
	padding: var(--spacer-30) var(--spacer-20) var(--spacer-20) var(--spacer-30);
	flex-direction: column;

	&:not(&--sticky-header) {
		margin-right: 0 !important;

		&:before,
		&:after {
			display: block;
			content: '';
			position: absolute;
			left: 0;
			right: 0;
			height: var(--spacer-30);
			z-index: 10;
			user-select: none;
			pointer-events: none;
			background: rgb(255, 255, 255);
			width: 100%;

			.browser-chrome & {
				width: calc(100% - var(--spacer-30));
			}

			.item-single--in-depth-reviews-layout & {
				content: none;
			}
		}

		&:after {
			background: linear-gradient(0deg, rgba(255, 255, 255, 1) 50%, rgba(255, 255, 255, 0) 100%);
			bottom: var(--spacer-20);
		}

		&:before {
			background: linear-gradient(180deg, rgba(255, 255, 255, 1) 50%, rgba(255, 255, 255, 0) 100%);
			top: calc( var(--spacer-60) + 5px );
		}
	}

	&:not(&--sticky-header) &__list-wrapper {
		height: calc(100% - var(--spacer-60) - 3px);
	}

	&:not(&--sticky-header) &__list {
		padding-top: var(--spacer-10);
	}

	@media (--viewport-full) and (min-height: 600px) {
		display: flex;
		max-height: var(--toc-max-height);
	}

	@media (--viewport-full) and (min-height: 720px) {
		max-height: var(--toc-max-height);
	}

	&__title {
		@mixin t-overline;
		color: var(--color-neutral-50);
		margin-bottom: calc(var(--spacer-10) + 4px);
		position: relative;
		display: none;

		@media (--viewport-full) and (min-height: 600px) {
			display: block;
		}
	}

	&__list {
		counter-reset: ToCtools;
		height: 100%;
		overflow-y: auto;
		padding-bottom: var(--spacer-10);
		padding-right: 8px;
		width: 100%;
		@mixin scrollbar-neutral;

		@media (max-height: 599px) {
			height: 100%;
		}

		&-tools-controller {
			counter-reset: menutoolstool;
		}

		ol {
			display: flex;
			flex-direction: column;
			gap: var(--spacer-10);
			transition: padding 300ms;
		}

		&__item {
			@mixin t-body-small;

			&.active {
				.c-table-of-contents__list__link {
					font-weight: var(--font-weight-semibold);
					color: var(--color-primary);
				}
			}
		}

		&__link {
			&:not(.c-table-of-contents__list__link--tool) {
				display: flex;

				&:before {
					content: "•";
					margin-right: var(--spacer-10);
					font-size: var(--font-size-body-large);
					line-height: 1.5rem;
				}
			}

			&:before {
				font-weight: var(--font-weight-regular);
				color: var(--color-text);
			}

			&:hover {
				font-weight: var(--font-weight-semibold);
				color: var(--color-primary);

				.c-sidebar-post__sidebar-item--toc-menu__list__item {
					font-weight: var(--font-weight-semibold);
					color: var(--color-primary);
				}

				&:before {
					font-weight: var(--font-weight-regular);
					color: var(--color-text);
				}
			}

			&--tool {
				display: grid;
				grid-template-columns: 20px auto;
				gap: var(--spacer-10);
				grid-auto-flow: column;
				align-items: center;
				counter-increment: menutoolstool;
				margin-left: 1em;
				list-style-type: none;

				&:before {
					content: counter(menutoolstool) '.';
				}
			}

			&--tool-full-listing {
				margin-left: 0;
			}
		}
	}

	&--full-height {
		max-height: calc(100vh - var(--header-size) - var(--spacer-20) * 3);
		height: max-content;

		.c-table-of-contents__list {
			height: 100%;
			max-height: calc((100vh - var(--header-size) - var(--spacer-20) * 3) - var(--spacer-60) - 3px);

			@media (min-height: 600px) {
				max-height: calc(100vh - var(--header-size) - var(--spacer-20) * 6 - var(--spacer-60) - 3px);

				.item-single--in-depth-reviews-layout & {
					min-height: var(--spacer-90);
				}
			}
		}
	}

	&__cta-wrapper {
		display: none;

		@media (min-height: 600px) {
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: calc(var(--spacer-30) - 4px);
			margin-right: var(--spacer-10);
			margin-bottom: var(--spacer-20);
			padding-top: var(--spacer-20);
		}

		&:not(:only-child) {
			padding-top: var(--spacer-30);
			border-top: 1px solid var(--color-neutral-20);
			margin-top: var(--spacer-30);
		}

		&.t-sink {
			a {
				&.a-cta--secondary {
					height: 30px;

					@media (max-height: 800px) {
						display: none;
					}
				}
			}
		}
	}

	&--sticky-header {
		--toc-sticky-top: 90px;
		padding: 0;
		width: 100%;
		max-width: 100%;
		border: solid 1px var(--color-neutral-20);
		border-radius: var(--border-radius-base);

		@media (--viewport-medium) {
			border: none;
			border-radius: 0;
		}

		.l-sink:not(.item-single__regwall-protected) > &:not(.c-block):not(.alignwide):not(.alignfull):not(.wp-block-group):not(.item-single__regwall-protected) {
			width: 100%;
			max-width: 100%;

			@media (--viewport-medium) {
				max-width: var(--grid-width-staggered-double);
			}
		}
	}

	&--sticky-header &__list {
		max-height: 0;
		padding-bottom: 0;
		overflow: hidden;
		transition: max-height 500ms, margin-top 500ms, margin-bottom 500ms, border 500ms;

		ol {
			padding: var(--spacer-30);
			padding-top: 0;

			@media (--viewport-medium) {
				padding: var(--spacer-30) 0 0 0;
			}
		}

		@media (--viewport-medium) {
			max-height: 1000px;
			transition: none;
		}
	}

	&--expanded &__list {
		max-height: 1000px;
		transition: max-height 1s, margin-top 1s, margin-bottom 1s, border 1s;

		@media (--viewport-medium) {
			transition: none;
		}
	}

	&--sticky-header &__title {
		display: block;
		margin-bottom: 0;
		padding: var(--spacer-20);
		cursor: pointer;

		@media (--viewport-medium) {
			border: none;
			padding: 0;
			cursor: initial;
		}

		&::after {
			display: block;
			position: absolute;
			content: var(--icon-chevron-down);
			font-family: var(--font-family-core-icons);
			font-size: var(--font-size-body-large);
			top: 50%;
			transform: translateY(-50%);
			right: var(--spacer-20);
			transition: transform 300ms;

			@media (--viewport-medium) {
				display: none;
			}
		}
	}

	&--locked {
		background-color: var(--color-white);
		z-index: 8;
		position: fixed;
		padding: var(--spacer-20) var(--spacer-30);
		border-top: 0;
		border-left: 0;
		border-right: 0;
		border-radius: 0;
		top: var(--toc-sticky-top);
		transition: padding 300ms;

		.l-sink:not(.item-single__regwall-protected) > &:not(.c-block):not(.alignwide):not(.alignfull):not(.wp-block-group):not(.item-single__regwall-protected) {
			width: 100vw;
			max-width: 100vw;
			margin-left: calc(var(--grid-margin-small) * -1);

			@media (--viewport-medium) {
				max-width: 500px;
				margin-left: 0;
			}
		}

		@media (--viewport-medium) {
			padding: 0;
			left: 50%;
			transform: translateX(-50%);
			background-color: var(--color-neutral-10);
			border: solid 1px var(--color-neutral-20);
			border-radius: var(--border-radius-base);
			margin-top: var(--spacer-20);
			box-shadow: var(--box-shadow-10);
		}
	}

	&--locked &__title {
		padding: var(--spacer-20);
		border: solid 1px var(--color-neutral-20);
		border-radius: var(--border-radius-base);
		margin-bottom: 0;
		position: relative;

		&::after {
			display: block;
		}

		@media (--viewport-medium) {
			border: none;
			border-radius: 0;
			cursor: pointer;
			padding: var(--spacer-20);
		}

		&[data-active-text]::before {
			@media (--viewport-medium) {
				content: '\2022' '\2009' ' ' attr(data-active-text);
				position: absolute;
				font-size: var(--font-size-body-small);
				left: 45%;
				width: 45%;
				top: 50%;
				transform: translateY(-50%);
				font-weight: var(--font-weight-medium);
				text-transform: none;
				letter-spacing: 0;
			}
		}

		&::after {
			@media (--viewport-medium) {
				display: block;
			}
		}

	}

	&--expanded &__title::after {
		transform: translateY(-50%) rotate(180deg);
	}

	&--locked &__list {
		overflow-y: scroll;
		padding-left: 0;
		padding-bottom: 0;
		transition: none;
		@mixin scrollbar-neutral-darker;
		@mixin scrollbar-transparent-padding;

		ol {
			padding: 0;
		}

		@media (--viewport-medium) {
			margin: 0;
			width: 100%;
			border-bottom: solid 1px transparent;
			max-height: 0;
		}
	}

	.t-sink &--locked &__list li {
		a {
			text-decoration: none;
		}

		&:not(.active) a {
			font-weight: var(--font-weight-regular);
			color: var(--color-black);
		}
	}

	&--expanded.c-table-of-contents--locked {
		padding: var(--spacer-20) var(--spacer-30) 0 var(--spacer-30);

		@media (--viewport-medium) {
			padding: 0;
		}
	}

	&--expanded.c-table-of-contents--locked &__list {
		max-height: 180px;
		padding: 0;
		width: 100%;

		ol {
			padding: var(--spacer-20) 0;
		}

		@media (--viewport-medium) {
			margin: 0;
		}
	}

	&--expanded.c-table-of-contents--locked &__title {
		border-bottom: solid 1px var(--color-neutral-20);
	}

	&--transition &__list {
		transition: max-height 500ms, margin 500ms, border 500ms;
	}

	&--locked &__list-wrapper {
		position: relative;

		@media (--viewport-medium) {
			margin: 0 var(--spacer-30);
		}

		&::before,
		&::after {
			content: '';
			display: block;
			width: calc(100% - var(--spacer-20));
			height: 20px;
			opacity: 0;
			transition: opacity 300ms;
			left: 0;
			position: absolute;
		}

		&::before {
			top: 0;
			background: rgb(255,255,255);
			background: linear-gradient(0deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.6) 25%, rgba(255,255,255,1) 53%);
		}

		&::after {
			bottom: 0;
			background: rgb(255,255,255);
			background: linear-gradient(180deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.6) 25%, rgba(255,255,255,1) 53%);
		}

		@media (--viewport-medium) {
			&::before {
				background: rgb(247,249,251);
				background: linear-gradient(0deg, rgba(247,249,251,0) 0%, rgba(247,249,251,0.6) 25%, rgba(247,249,251,1) 53%);
			}

			&::after {
				background: rgb(247,249,251);
				background: linear-gradient(180deg, rgba(247,249,251,0) 0%, rgba(247,249,251,0.6) 25%, rgba(247,249,251,1) 53%);
			}
		}
	}

	&--expanded &__list-wrapper {
		&::before,
		&::after {
			opacity: 1;
		}
	}
}

.c-sidebar-post {
	&:has(.c-table-of-contents--full-height) {
		.c-sidebar-post__sidebar-item {
			&:not(.c-sidebar-post__sidebar-item--toc-menu) {
				display: none !important;
			}
		}
	}

	&-item {
		&:has(.c-table-of-contents) {
			display: flex !important;
		}

		&--toc-menu {
			max-height: var(--toc-max-height);
		}
	}

	&--fixed {
		&:not(:has(.c-table-of-contents--full-height)) {
			--toc-max-height: 480px;
			grid-template-rows: var(--toc-max-height) 1fr;

			@media (--viewport-full) and (min-height: 600px) {
				--toc-max-height: 240px;
			}

			@media (--viewport-full) and (min-height: 720px) {
				--toc-max-height: 290px;
			}
		}

		.c-sidebar-post__sidebar-item {
			top: calc(var(--header-size) + var(--spacer-40) + var(--spacer-10) + var(--toc-max-height));

			.item-single--in-depth-reviews-layout & {
				&.c-sidebar-post__sidebar-item--toc-menu {
					top: calc(var(--header-size) + var(--spacer-40));
				}
			}

			.c-table-of-contents {
				height: var(--toc-max-height);

				/* @media (--viewport-full) and (min-height: 600px) {
					height: calc(var(--toc-max-height) - var(--spacer-20) - var(--font-size-body-xsmall));
				} */
			}

			&--toc-menu {
				max-height: var(--toc-max-height);
				background-color: var(--color-neutral-0);

				.c-sidebar-post__sidebar-item--links-menu__container {
					margin-right: 8px;
				}

				.item-single--in-depth-reviews-layout & {
					max-height: max-content;
				}
			}
		}

		@media (--viewport-large) {
			&:not(:has(.c-table-of-contents--full-height)):not(:has(.c-table-of-contents)):not(:has(.c-ad-block__holder)):not(:has(> div:nth-child(3))) {
				--toc-max-height: auto;

				> div {
					top: calc(var(--header-size) + var(--spacer-20));
					position: sticky;

					&:nth-child(2) {
						align-self: center;
						z-index: 2;
						background-color: var(--color-white);
					}
				}
			}
		}
	}
}

.item-single--perfect-listicle-layout {
	.c-sidebar-post__sidebar-item--toc-menu {
		display: flex;
		flex-direction: column;
		max-height: calc(100vh - var(--header-size) - 50px);
		height: 100%;
		padding-top: var(--spacer-10);
	}
}

.item-single--in-depth-reviews-layout {
	.c-sidebar-post {
		padding-bottom: var(--spacer-80);
		--toc-container-max-height: 0;

		&:has(.c-sidebar-post__sidebar-video) {
			--toc-container-max-height: calc( 180px + var(--spacer-40) );
			padding-bottom: calc( var(--spacer-80) + 180px + var(--spacer-40) );
		}

		&__item-container {
			max-height: calc( var(--toc-max-height) + var(--toc-container-max-height) );
		}
	}
}
