<?php
declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\blocks\simple_embed;

use Tribe\Project\Blocks\Types\Simple_Embed\Simple_Embed;
use Tribe\Project\Integrations\GSAP\GSAP;
use Tribe\Project\Post_Types\Page\Page;
use Tribe\Project\Templates\Components\Abstract_Controller;
use Tribe\Libs\Utils\Markup_Utils;
use Tribe\Project\Templates\Components\Deferred_Component;
use Tribe\Project\Templates\Components\video\Video_Controller;

class Simple_Embed_Block_Controller extends Abstract_Controller {
	public const CLASSES           = 'classes';
	public const ATTRS             = 'attrs';
	public const CONTAINER_CLASSES = 'container_classes';
	public const LINK              = 'link';
	public const LABEL             = 'label';
	public const THUMBNAIL         = 'thumbnail';
	public const ALIGNMENT         = 'alignment';

	public const MICROINTERACTIONS_PLAY_BUTTON = 'microinteractions_play_button';

	private array  $classes;
	private array  $attrs;
	private string $link;
	private string $label;
	private int    $thumbnail;
	private array  $container_classes;
	private string $alignment;
	private bool   $microinteractions_play_button;

	/**
	 * @param array $args
	 */
	public function __construct( array $args = [] ) {
		$args = $this->parse_args( $args );

		$this->classes                       = (array) $args[ self::CLASSES ];
		$this->attrs                         = (array) $args[ self::ATTRS ];
		$this->link                          = (string) $args[ self::LINK ];
		$this->label                         = (string) $args[ self::LABEL ];
		$this->thumbnail                     = (int) $args[ self::THUMBNAIL ];
		$this->container_classes             = (array) $args[ self::CONTAINER_CLASSES ];
		$this->alignment                     = (string) $args[ self::ALIGNMENT ];
		$this->microinteractions_play_button = (bool) $args[ self::MICROINTERACTIONS_PLAY_BUTTON ];

		if ( get_post_type() !== Page::NAME ) {
			$this->microinteractions_play_button = false;
		}

		$this->get_microinteractions_scripts();
	}

	protected function defaults(): array {
		return [
			self::CLASSES                       => [],
			self::ATTRS                         => [],
			self::LINK                          => '',
			self::LABEL                         => '',
			self::THUMBNAIL                     => 0,
			self::CONTAINER_CLASSES             => [],
			self::ALIGNMENT                     => '',
			self::MICROINTERACTIONS_PLAY_BUTTON => false,
		];
	}

	protected function required(): array {
		return [
			// Note: This block does not use `c-block` intentionally.
			self::CLASSES           => [ 'b-simple_embed' ],
			self::CONTAINER_CLASSES => [ 'b-simple_embed__container' ],
		];
	}

	/**
	 * @return string
	 */
	public function get_classes(): string {
		$this->classes[] = $this->get_alignment();

		return Markup_Utils::class_attribute( $this->classes );
	}

	/**
	 * @return string
	 */
	public function get_attrs(): string {
		return Markup_Utils::concat_attrs( $this->attrs );
	}

	/**
	 * @return string
	 */
	public function get_alignment(): string {
		return $this->alignment ? 'has-text-align-' . $this->alignment : '';
	}

	/**
	 * @return string
	 */
	public function get_container_classes(): string {
		if ( $this->get_microinteractions_play_button() ) {
			$this->container_classes[] = 'b-simple_embed__container--has-microinteraction--play-button';
		}

		return Markup_Utils::class_attribute( $this->container_classes );
	}

	public function is_valid_link(): bool {
		return false !== filter_var( $this->link, FILTER_VALIDATE_URL );
	}

	public function get_embed_content(): string|Deferred_Component {
		if ( ! $this->is_valid_link() ) {
			if ( is_admin() ) {
				return '<pre>Error: Link is not valid</pre>';
			}

			return '';
		}

		$provider = $this->get_provider();

		if ( ! $provider ) {
			if ( is_admin() ) {
				return '<pre>Error: Provider not valid</pre>';
			}

			return '';
		}

		switch ( $provider ) {
			case Simple_Embed::SIMPLE_INSTAGRAM:
				return $this->get_embed_instagram();
			case Simple_Embed::SIMPLE_TIKTOK:
				return $this->get_embed_tiktok();
			case Simple_Embed::SIMPLE_SPOTIFY:
				return $this->get_embed_spotify();
			case Simple_Embed::SIMPLE_YOUTUBE:
				return $this->get_embed_youtube();
			case Simple_Embed::SIMPLE_VIMEO:
				return $this->get_embed_vimeo();
			default:
				return '';
		}
	}

	private function get_link(): string {
		return rtrim( trim( $this->link ), "/" );
	}

	private function get_provider(): string|null {
		$url = parse_url( $this->get_link() );

		if ( ! $url || ! isset( $url['host'] ) ) {
			return null;
		}

		switch ( $url['host'] ) {
			case 'www.instagram.com':
			case 'instagram.com':
				return Simple_Embed::SIMPLE_INSTAGRAM;
			case 'tiktok.com':
				return Simple_Embed::SIMPLE_TIKTOK;
			case 'spotify.com':
			case 'open.spotify.com':
				return Simple_Embed::SIMPLE_SPOTIFY;
			case 'www.youtube.com':
			case 'youtube.com':
			case 'youtu.be':
				return Simple_Embed::SIMPLE_YOUTUBE;
			case 'vimeo.com':
			case 'player.vimeo.com':
				return Simple_Embed::SIMPLE_VIMEO;
			default:
				return null;
		}
	}

	private function get_embed_instagram(): string {
		return sprintf( '<blockquote class="instagram-media" data-instgrm-permalink="%s/?utm_source=ig_embed&amp;utm_campaign=loading" data-instgrm-version="14" style=" background:#FFF; border:0; border-radius:3px; box-shadow:0 0 1px 0 rgba(0,0,0,0.5),0 1px 10px 0 rgba(0,0,0,0.15); margin: 1px; max-width:540px; min-width:326px; padding:0; width:99.375%%; width:-webkit-calc(100%% - 2px); width:calc(100%% - 2px);"></blockquote><script async src="//www.instagram.com/embed.js"></script>', $this->get_link() );
	}

	private function get_embed_tiktok(): string {
		$tiktok_url   = parse_url( $this->get_link(), PHP_URL_PATH );
		$tiktok_paths = explode( '/', $tiktok_url );
		$tiktok_id    = end( $tiktok_paths );

		// Probably a wrong URL
		if ( ! $tiktok_id ) {
			return '';
		}

		// Embed it as a profile
		if ( str_starts_with( $tiktok_id, '@' ) ) {
			$tiktok_id = str_replace( '@', '', $tiktok_id );

			return sprintf( '<blockquote class="tiktok-embed" cite="%s" data-unique-id="%s" data-embed-type="creator"><section></section></blockquote><script async src="https://www.tiktok.com/embed.js"></script>', $this->get_link(), $tiktok_id );
		}

		// Embed the video
		return sprintf( '<blockquote class="tiktok-embed" cite="%s" data-video-id="%s"><section></section></blockquote><script async src="https://www.tiktok.com/embed.js"></script>', $this->get_link(), $tiktok_id );
	}

	private function get_embed_spotify(): string {
		$spotify_domain = parse_url( $this->get_link(), PHP_URL_HOST );
		$spotify_url    = parse_url( $this->get_link(), PHP_URL_PATH );
		$spotify_url    = implode( '/', array_slice( explode( '/', $spotify_url ), - 2, 2, true ) );
		$spotify_embed  = 'https://' . $spotify_domain . '/embed/' . $spotify_url . '?utm_source=' . get_bloginfo( 'url' );

		return sprintf( '<iframe style="border-radius:var(--border-radius-media);" src="%s" width="100%%" height="352" frameBorder="0" allowfullscreen="" allow="autoplay; clipboard-write; encrypted-media; fullscreen; picture-in-picture" loading="lazy"></iframe>', $spotify_embed );
	}

	private function get_video_id(): string {
		$url = parse_url( $this->get_link() );

		if ( ! $url ) {
			return '';
		}

		if ( $url['host'] === 'youtu.be' ) {
			// get the path as video_id
			$video_id = ltrim( $url['path'], '/' );
		} elseif ( $url['host'] === 'vimeo.com' || $url['host'] === 'player.vimeo.com' ) {
			$pattern = '/(?:vimeo\.com\/(?:.*?\/)?|player\.vimeo\.com\/video\/)(\d+)/';

			if ( preg_match( $pattern, $this->get_link(), $matches ) ) {
				$video_id = $matches[1];
			}
		} elseif ( str_contains( $url['host'], 'youtube' ) && str_contains( $url['path'], 'shorts' ) ) {
			$shorts_path = str_replace( 'shorts', '', $url['path'] );
			$video_id    = ltrim( $shorts_path, '/' );
		} elseif ( str_contains( $url['host'], 'youtube' ) && str_contains( $url['path'], 'live' ) ) {
			// Handle YouTube live URLs like https://www.youtube.com/live/1HVoYr5jg58
			$live_path = str_replace( '/live/', '', $url['path'] );
			$video_id  = ltrim( $live_path, '/' );
		} else {
			$query = [];
			parse_str( $url['query'], $query );

			if ( ! isset( $query['v'] ) ) {
				return '';
			}

			$video_id = $query['v'];
		}

		if ( ! $video_id ) {
			return '';
		}

		return $video_id;
	}

	private function get_embed_youtube(): string|Deferred_Component {
		if ( $this->thumbnail === 0 ) {
			$video_id = $this->get_video_id();

			$thumbnail = "https://i.ytimg.com/vi/$video_id/maxresdefault.jpg";
		} else {
			$thumbnail = wp_get_attachment_image_url( $this->thumbnail, 'full' );
		}

		$label = ! empty( $this->label ) ? $this->label : __( 'Play Video', 'tribe' );

		if ( $this->get_microinteractions_play_button() ) {
			$label = null;
		}

		return defer_template_part( 'components/video/video', null, [
			Video_Controller::VIDEO_URL     => $this->get_link(),
			Video_Controller::CLASSES       => [ 'b-simple_embed__video' ],
			Video_Controller::THUMBNAIL_URL => $thumbnail,
			Video_Controller::TRIGGER_LABEL => $label,
			Video_Controller::ATTRS         => $this->get_video_layout_container_attrs( Simple_Embed::SIMPLE_YOUTUBE, $this->get_video_id(), $this->label ),
		] );
	}

	public function get_embed_vimeo(): ?string {
		$link = $this->get_link();

		if ( ! $link ) {
			return null;
		}

		if ( ! str_contains( $link, 'player.vimeo.com' ) ) {
			$link = str_replace( 'vimeo.com', 'player.vimeo.com/video', $link );
		}

		$link = sprintf( '<iframe src="%s" frameborder="0" style="width:792px;aspect-ratio:16/9;max-width:100%%;"></iframe>', $link );

		$link = apply_filters( 'vimeo_clear_link', $link );

		if ( ! str_contains( $link, 'autoplay=1' ) ) {
			return $link;
		}

		if ( is_admin() ) {
			return str_replace( 'autoplay=1', 'autoplay=0', $link );
		}

		return $link;
	}

	public function get_video_layout_container_attrs( $provider_name, $embed_id, $title ): array {
		return [
			'data-js'             => 'c-video',
			'data-embed-id'       => $embed_id,
			'data-embed-provider' => $provider_name,
			'data-embed-title'    => $title,
		];
	}

	public function get_microinteractions_play_button(): bool {
		return $this->microinteractions_play_button;
	}

	public function get_microinteractions_scripts(): void {
		if ( $this->get_microinteractions_play_button() ) {
			GSAP::add_script( 'Simple_Embed_' . self::MICROINTERACTIONS_PLAY_BUTTON, $this->get_microinteractions_play_button_gsap_script() );
		}
	}

	private function get_microinteractions_play_button_gsap_script(): ?string {
		if ( wp_is_mobile() ) {
			return null;
		}

		return <<<JAVASSCRIPT
			<script>
				document.addEventListener("bwz/gsap_loaded", (event) => {
					if ( typeof gsap === 'undefined' ) { return; }

					const containers = document.querySelectorAll('.b-simple_embed__container--has-microinteraction--play-button');

					containers.forEach(container => {
						const trigger = container.querySelector('.c-video__trigger-action');

						const containerRect = container.getBoundingClientRect();
						const initialX = containerRect.width / 2;
						const initialY = containerRect.height / 2;

						container.style.position = 'relative';

						gsap.set(trigger, {
							position: 'absolute',
							top: initialY + `px`,
							left: initialX + `px`,
							pointerEvents: 'none',
							scale: 1.2,
							opacity: 1,
							transform: 'translate(-50%, -50%)',
							cursor: 'none',
						});

						container.addEventListener('mouseenter', (e) => {
							const x = e.clientX - container.getBoundingClientRect().left;
							const y = e.clientY - container.getBoundingClientRect().top;

							gsap.to(trigger, {
								left: x + `px`,
								top: y + `px`,
								duration: 0.2,
								scale: 1.4,
								ease: 'power1.out'
							});
						});

						container.addEventListener('mousemove', (e) => {
							const x = e.clientX - container.getBoundingClientRect().left;
							const y = e.clientY - container.getBoundingClientRect().top;

							gsap.to(trigger, {
								left: x + `px`,
								top: y + `px`,
								duration: 0.2,
								ease: 'none'
							});
						});

						container.addEventListener('mouseleave', () => {
							gsap.to(trigger, {
								left: initialX + `px`,
								top: initialY + `px`,
								duration: 0.8,
								scale: 1.2,
								ease: 'power1.inOut'
							});
						});
					});
				});
			</script>
		JAVASSCRIPT;
	}
}
