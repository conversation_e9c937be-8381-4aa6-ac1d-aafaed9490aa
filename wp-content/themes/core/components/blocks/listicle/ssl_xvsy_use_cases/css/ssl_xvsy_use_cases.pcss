/* -----------------------------------------------------------------------------
 *
 * SSL XvsY Use Cases
 *
 * ----------------------------------------------------------------------------- */

.b-ssl-xvsy-use-cases {
	h4 {
		font-size: var(--font-size-heading-xsmall);
		font-weight: var(--font-weight-semibold);
		border-bottom: solid 2px var(--color-neutral-20);
		padding-bottom: var(--spacer-20);
		width: 100%;
	}

	figcaption {
		border: none;
		text-align: center;
		margin: var(--spacer-20) 0;
		padding: 0;

		@media (--viewport-medium) {
			display: flex;
			justify-content: center;
			align-items: center;
			height: var(--spacer-60);
			margin: var(--spacer-10) 0;
		}
	}

	&__figcaption-placeholder {
		@media (--viewport-medium) {
			display: block;
			height: var(--spacer-60);
			margin: var(--spacer-10) 0;
		}
	}

	.c-slider {
		overflow: hidden;

		.swiper-slide {
			padding: 0;

			@media (--viewport-large) {
				flex-direction: row;
				padding: 0 var(--spacer-60);
			}

			img {
				width: 100%;
				height: 280px;
				max-width: 500px;
				max-height: 45vw;
				object-fit: cover;
				border: solid 1px var(--color-neutral-20);
				margin-bottom: 0;
			}
		}

		.swiper-button-prev {
			left: 0;
		}

		.swiper-button-next {
			right: 5px;
		}

		.swiper-button-prev,
		.swiper-button-next {
			transform: translateY(-50%);
		}

		.swiper-button-prev::after,
		.swiper-button-next::after {
			color: var(--color-black);
		}

		&__arrows {
			margin: 0;
		}

		&__pagination {
			@media (--viewport-large) {
				display: none;
			}
		}
	}

	&__comparison {
		display: flex;
		flex-direction: column;
		gap: var(--spacer-30);
		justify-content: space-between;

		@media (--viewport-large) {
			flex-direction: row;
		}
	}

	&__column {
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		gap: var(--spacer-30);

		@media (--viewport-large) {
			width: calc(50% - var(--spacer-20));
		}

		&:last-child {
			@media (--viewport-medium-max) {
				margin-top: var(--spacer-40);
			}
		}
	}

	&__screenshots {
		width: 100%;
	}

	&__list {
		display: grid;
		gap: var(--spacer-40);
		margin-bottom: var(--spacer-10);
		list-style: auto;
		padding-left: var(--spacer-30);

		li::marker {
			font-weight: var(--font-weight-semibold);
			font-size: var(--font-size-body);
			display: inline-block;
		}

		b {
			display: block;
			margin-bottom: var(--spacer-20);
			font-size: var(--font-size-body);
			font-weight: var(--font-weight-semibold);
		}
	}

	&__quote {
		border-top: solid 1px var(--color-neutral-20);

		&__container {
			display: flex;
			justify-content: center;
			padding: var(--spacer-50) var(--spacer-10) 0 var(--spacer-10);
			align-items: center;
			gap: var(--spacer-40);
			flex-wrap: wrap;

			@media (--viewport-medium) {
				width: calc(100% - var(--grid-gutter) * 2);
				max-width: var(--grid-width-staggered-double);
				margin: auto;
				flex-wrap: nowrap;
			}

			p {
				width: 100%;
				text-align: center;

				@media (--viewport-medium) {
					width: auto;
				}
			}

			a {
				white-space: nowrap;
			}
		}
	}
}
