/* -----------------------------------------------------------------------------
 *
 * SSL XvsY Feature Comparison
 *
 * ----------------------------------------------------------------------------- */

.b-ssl-xvsy-feature-comparison {
	:not(.acf-table) .t-sink & {
		table {
			margin-top: 0;
			table-layout: auto;

			@media (--viewport-medium) {
				table-layout: fixed;
				width: calc(100% - var(--grid-gutter) * 2);
				max-width: var(--grid-width-staggered-double);
				margin: auto;
			}

			th {
				padding: var(--spacer-10);
			}

			td {
				font-size: var(--font-size-heading-small);
				padding: 0;
			}

			i {
				font-size: var(--font-size-heading);
				line-height: 1;
				display: block;
				color: var(--color-neutral-30);

				&.icon-check {
					color: var(--color-primary);
				}
			}
		}

		table, tr, th, td {
			border: none;
		}

		tbody {
			tr:nth-child(odd) {
				background-color: var(--color-grey-light);
			}

			tr:nth-child(even) {
				background-color: var(--color-white);
			}

			th {
				font-weight: normal;
			}
		}

		th {
			font-size: var(--font-size-body-xsmall);

			@media (--viewport-medium) {
				font-size: var(--font-size-body-small);
			}
		}

		th:not(:first-child), td {
			text-align: center;

			@media (--viewport-large) {
				width: 15%;
			}
		}
	}

	&__quote {
		border-top: solid 1px var(--color-neutral-20);

		&__container {
			display: flex;
			justify-content: center;
			padding: var(--spacer-50) var(--spacer-10) 0 var(--spacer-10);
			align-items: center;
			gap: var(--spacer-40);
			flex-wrap: wrap;

			@media (--viewport-medium) {
				width: calc(100% - var(--grid-gutter) * 2);
				max-width: var(--grid-width-staggered-double);
				margin: auto;
				flex-wrap: nowrap;
			}

			p {
				width: 100%;
				text-align: center;

				@media (--viewport-medium) {
					width: auto;
				}
			}

			a {
				white-space: nowrap;
			}
		}
	}

	&__table-container {
		overflow: hidden;
		position: relative;

		&--has-more {
			padding-bottom: var(--spacer-70);
		}

		&--has-more:not(&--expanded) {
			max-height: 290px;
		}

		&--expanded &__show-more button::after {
			transform: rotate(180deg);
		}

		&__show-more {
			width: 100%;
			position: absolute;
			bottom: 0;
			text-align: center;
			background: rgb(255, 255, 255);
			background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.6) 25%, rgba(255, 255, 255, 1) 50%);
			padding: var(--spacer-30) 0;

			button {
				border: none;
				background-color: transparent;
				font-size: var(--font-size-body-xsmall);
				font-weight: var(--font-weight-semibold);
				padding: 4px;

				&::after {
					content: "\e90b";
					font-family: var(--font-family-core-icons);
					color: var(--color-black);
					margin-left: 2px;
					position: relative;
					display: inline-block;
				}
			}
		}
	}
}
