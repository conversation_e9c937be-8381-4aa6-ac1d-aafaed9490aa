/* -----------------------------------------------------------------------------
 *
 * SSL XvsY Pricing Comparison
 *
 * ----------------------------------------------------------------------------- */

.b-ssl-xvsy-pricing-comparison {
	:not(.acf-table) .t-sink & {
		table {
			margin-top: 0;
			table-layout: auto;
			min-width: 500px;

			@media (--viewport-medium) {
				table-layout: fixed;
				width: calc(100% - var(--grid-gutter) * 2);
				max-width: var(--grid-width-staggered-double);
				margin: auto;
			}

			th {
				padding: var(--spacer-10);

				th:first-child {
					padding-left: var(--spacer-20);
				}
			}

			i {
				font-size: var(--font-size-heading);
				line-height: 1;
				display: block;
				color: var(--color-neutral-30);

				&.icon-check {
					color: var(--color-primary);
				}
			}
		}

		table, tr, th, td {
			border: none;
		}

		th, td {
			font-size: var(--font-size-body-xsmall);

			@media (--viewport-medium) {
				font-size: var(--font-size-body-small);
			}
		}

		tbody {
			tr:nth-child(odd) {
				background-color: var(--color-grey-light);
			}

			tr:nth-child(even) {
				background-color: var(--color-white);
			}

			th {
				font-weight: var(--font-weight-bold);
				text-transform: uppercase;

				@media (--viewport-medium) {
					font-weight: var(--font-weight-regular);
					text-transform: none;
				}
			}
		}

		th:not(:first-child), td {
			text-align: center;
		}
	}

	&__quote {
		border-top: solid 1px var(--color-neutral-20);

		&__container {
			display: flex;
			justify-content: center;
			padding: var(--spacer-50) var(--spacer-10) 0 var(--spacer-10);
			align-items: center;
			gap: var(--spacer-40);
			flex-wrap: wrap;

			@media (--viewport-medium) {
				width: calc(100% - var(--grid-gutter) * 2);
				max-width: var(--grid-width-staggered-double);
				margin: auto;
				flex-wrap: nowrap;
			}

			p {
				width: 100%;
				text-align: center;

				@media (--viewport-medium) {
					width: auto;
				}
			}

			a {
				white-space: nowrap;
			}
		}
	}

	&__table-container {
		overflow-x: scroll;
		padding-right: var(--spacer-20);

		@media (--viewport-medium) {
			overflow-x: initial;
		}
	}

	&__table-shadow {
		position: relative;

		&::after {
			content: '';
			display: block;
			position: absolute;
			right: calc(var(--spacer-20) * -1);
			top: 0;
			height: 100%;
			width: var(--spacer-60);
			background: rgb(255, 255, 255);
			background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.6) 25%, rgba(255, 255, 255, 1) 50%);

			@media (--viewport-medium) {
				display: none;
			}
		}
	}
}
