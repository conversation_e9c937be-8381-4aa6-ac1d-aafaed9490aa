<?php
declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\blocks\video_gallery;

use Tribe\Libs\Utils\Markup_Utils;
use Tribe\Project\Blocks\Types\Video_Gallery\Video_Gallery;
use Tribe\Project\Object_Meta\Ctas_Meta;
use Tribe\Project\Templates\Components\Abstract_Controller;
use Tribe\Project\Templates\Components\author\Author_Controller;
use Tribe\Project\Templates\Components\container\Container_Controller;
use Tribe\Project\Templates\Components\content_block\Content_Block_Controller;
use Tribe\Project\Templates\Components\Deferred_Component;
use Tribe\Project\Templates\Components\image\Image_Controller;
use Tribe\Project\Templates\Components\link\Link_Controller;
use Tribe\Project\Templates\Components\text\Text_Controller;
use Tribe\Project\Templates\Components\Traits\Handles_MemberPress_Permissions;
use Tribe\Project\Templates\Models\Video_List_Item;
use Tribe\Project\Theme\Config\Image_Sizes;

class Video_Gallery_Block_Controller extends Abstract_Controller {

	use Handles_MemberPress_Permissions;

	public const TITLE             = 'title';
	public const DESCRIPTION       = 'description';
	public const CTA               = 'cta';
	public const LAYOUT            = 'layout';
	public const CONTAINER_CLASSES = 'container_classes';
	public const CONTENT_CLASSES   = 'content_classes';
	public const CLASSES           = 'classes';
	public const ATTRS             = 'attrs';
	public const LIST_OF_VIDEOS    = 'list_of_videos';
	public const GALLERY_TYPE      = 'gallery_type';
	public const CLASS_BASE        = 'b-video-gallery';
	public const HIDE_AUTHORS	   = 'hide_authors';

	private string $title;
	private string $description;
	private array  $cta;
	private string $layout;
	private array  $container_classes;
	private array  $content_classes;
	private array  $classes;
	private array  $attrs;
	private array  $list_of_videos;
	private string $gallery_type;
	private bool   $hide_authors;

	/**
	 * @param array $args
	 */
	public function __construct( array $args = [] ) {
		$args = $this->parse_args( $args );

		$this->title             = (string) $args[ self::TITLE ];
		$this->description       = (string) $args[ self::DESCRIPTION ];
		$this->cta               = (array) $args[ self::CTA ];
		$this->layout            = (string) $args[ self::LAYOUT ];
		$this->container_classes = (array) $args[ self::CONTAINER_CLASSES ];
		$this->content_classes   = (array) $args[ self::CONTENT_CLASSES ];
		$this->classes           = (array) $args[ self::CLASSES ];
		$this->attrs             = (array) $args[ self::ATTRS ];
		$this->list_of_videos    = (array) $args[ self::LIST_OF_VIDEOS ];
		$this->gallery_type      = (string) $args[ self::GALLERY_TYPE ];
		$this->hide_authors      = (bool) $args[ self::HIDE_AUTHORS ];
	}

	/**
	 * @return array
	 */
	protected function defaults(): array {
		return [
			self::TITLE             => '',
			self::DESCRIPTION       => '',
			self::CTA               => [],
			self::LAYOUT            => '',
			self::CONTAINER_CLASSES => [],
			self::CONTENT_CLASSES   => [],
			self::CLASSES           => [],
			self::ATTRS             => [],
			self::LIST_OF_VIDEOS    => [],
			self::GALLERY_TYPE      => '',
			self::HIDE_AUTHORS      => false,
		];
	}

	public function append_class_name( $class ): string {
		return self::CLASS_BASE . '__' . $class;
	}

	/**
	 * @return array
	 */
	protected function required(): array {
		return [
			self::CONTAINER_CLASSES => [ $this->append_class_name( 'container' ), 'l-container' ],
			self::CLASSES           => [ 'c-block', 'b-video-gallery' ],
			self::CONTENT_CLASSES   => [ $this->append_class_name( 'content' ) ],
		];
	}

	/**
	 * @return string
	 */
	public function get_classes(): string {
		$this->classes[] = 'b-video-gallery--' . str_replace( '_', '-', $this->layout );

		return Markup_Utils::class_attribute( $this->classes );
	}

	/**
	 * @return string
	 */
	public function get_attrs(): string {
		return Markup_Utils::concat_attrs( $this->attrs );
	}

	/**
	 * @return string
	 */
	public function get_container_classes(): string {
		return Markup_Utils::class_attribute( $this->container_classes );
	}

	/**
	 * @return string
	 */
	public function get_content_classes(): string {
		return Markup_Utils::class_attribute( $this->content_classes );
	}

	/**
	 * @return array
	 */
	public function get_header_args(): array {
		if ( empty( $this->title ) && empty( $this->description ) ) {
			return [];
		}

		return [
			Content_Block_Controller::TAG     => 'header',
			Content_Block_Controller::TITLE   => $this->get_title(),
			Content_Block_Controller::CONTENT => $this->get_content(),
			Content_Block_Controller::CTA     => $this->get_cta(),
			Content_Block_Controller::CLASSES => [
				'c-block__content-block',
				'c-block__header',
				$this->append_class_name( 'header' ),
			],
		];
	}

	/**
	 * @return Deferred_Component
	 */
	private function get_title(): Deferred_Component {
		return defer_template_part( 'components/text/text', null, [
			Text_Controller::TAG     => 'h2',
			Text_Controller::CLASSES => [
				'c-block__title',
				$this->append_class_name( 'title' ),
				'h3',
			],
			Text_Controller::CONTENT => $this->title ?? '',
		] );
	}

	/**
	 * @return Deferred_Component
	 */
	private function get_content(): Deferred_Component {
		return defer_template_part( 'components/container/container', null, [
			Container_Controller::CLASSES => [
				'c-block__description',
				$this->append_class_name( 'description' ),
				't-sink',
				's-sink',
			],
			Container_Controller::CONTENT => $this->description ?? '',
		] );
	}

	/**
	 * @return Deferred_Component
	 */
	private function get_cta(): Deferred_Component {
		$cta = wp_parse_args( $this->cta, [
			'content' => '',
			'url'     => '',
			'target'  => '',
		] );

		return defer_template_part( 'components/link/link', null, [
			Link_Controller::URL     => $cta['url'],
			Link_Controller::CONTENT => $cta['content'] ?: $cta['url'],
			Link_Controller::TARGET  => $cta['target'],
			Link_Controller::CLASSES => [
				'c-block__cta-link',
				'a-btn',
			],
		] );
	}

	/**
	 * @return Video_List_Item []
	 */
	public function get_content_args(): array {
		return $this->list_of_videos;
	}

	public function is_single(): bool {
		return $this->layout === Video_Gallery::LAYOUT_SINGLE;
	}

	public function get_hide_authors(): bool {
		return $this->hide_authors ?? false;
	}

	public function get_video_embed_code( Video_List_Item $video, bool $only_url = false ): ?string {
		if ( ! $video->url || ! isset( $video->video_provider ) ) {
			return null;
		}

		if ( $video->video_provider === Video_List_Item::VIDEO_PROVIDER_YOUTUBE ) {
			$url       = parse_url( $video->url );
			$embed_url = null;

			if ( isset( $url['path'] ) && $url['path'] && str_contains( $url['path'], '/embed' ) ) {
				if ( $url['query'] ) {
					$embed_url = str_replace('?' . $url['query'], '', $video->url);
				} else {
					$embed_url = $video->url;
				}
			} elseif ( $url['host'] === 'youtu.be' ) {
				$embed_url = "https://www.youtube.com/embed{$url['path']}";
			} elseif ( str_contains( $url['host'], 'youtube' ) && str_contains( $url['path'], 'live' ) ) {
				// Handle YouTube live URLs like https://www.youtube.com/live/1HVoYr5jg58
				$live_path = str_replace( '/live/', '', $url['path'] );
				$video_id  = ltrim( $live_path, '/' );
				$embed_url = "https://www.youtube.com/embed/{$video_id}";
			} elseif ( isset( $url['query'] ) ) {
				parse_str( $url['query'], $url_query );

				if ( isset( $url_query['v'] ) ) {
					$embed_url = "https://www.youtube.com/embed/{$url_query['v']}";
				}
			}

			if ( ! $embed_url ) {
				return null;
			}

			if ( $only_url ) {
				return $embed_url;
			}

			if ( ! filter_var( $only_url, FILTER_VALIDATE_URL ) ) {
				return null;
			}

			return <<<HTML
				<iframe
				src="{$embed_url}" title="{$video->title}"
				width="792" height="446"
				frameborder="0" allow="autoplay; web-share"
				allowfullscreen></iframe>
			HTML;
		}

		if ( $video->video_provider === Video_List_Item::VIDEO_PROVIDER_VIMEO ) {
			if ( $only_url ) {
				return $video->url;
			}

			$url       = parse_url( $video->url );
			$embed_url = null;

			if ( $url['path'] ) {
				$embed_url = "https://player.vimeo.com/video{$url['path']}";
			}

			if ( ! $embed_url ) {
				return null;
			}

			if ( ! filter_var( $embed_url, FILTER_VALIDATE_URL ) ) {
				return null;
			}

			return <<<HTML
				<iframe
				src="{$video->url}"
				width="640" height="1139" frameborder="0" allow="autoplay; fullscreen; picture-in-picture"
				allowfullscreen></iframe>
			HTML;
		}

		return null;
	}

	public function get_video_thumbnail_image( Video_List_Item $video, string $image_size = Image_Sizes::SIXTEEN_NINE, bool $only_url = false ): string|Deferred_Component {

		$img = wp_get_attachment_image_url( $video->thumbnail_id, $image_size );

		if ( $only_url ) {
			return esc_url( $img );
		}

		return defer_template_part( 'components/image/image', null, [
			Image_Controller::IMG_URL      => esc_url( $img ),
			Image_Controller::AS_BG        => true,
			Image_Controller::USE_LAZYLOAD => true,
			Image_Controller::CLASSES      => [ $this->append_class_name( 'thumbnail' ) ],
			Image_Controller::IMG_ALT_TEXT => $video->title ? esc_attr( $video->title ) : '',
			Image_Controller::IMG_CLASSES  => [ $this->append_class_name( 'thumbnail-img' ) ],
		] );
	}

	public function get_video_author_args( array $params = [] ): ?array {
		if ( ! array_key_exists( 'author_id', $params ) ) {
			return null;
		}

		$id = $params['author_id'];

		return [
			Author_Controller::AUTHOR_NAME      => Author_Controller::get_author_display_name( $id ),
			Author_Controller::AUTHOR_ID        => $id,
			Author_Controller::AVATAR_SIZE      => $params['size'] ?: 50,
			Author_Controller::SHOW_DESCRIPTION => $params['show_description'] ?: false,
			Author_Controller::AUTHOR_ABOUT     => Author_Controller::get_the_author_meta( 'user_description', $id ),
			Author_Controller::SHOW_LINK_NAME   => array_key_exists( 'show_link_name', $params ) ? $params['show_link_name'] : false,
		];
	}

	/**
	 * @return string|null
	 */
	public function get_membership_link(): ?string {
		$membership_url = get_field( Ctas_Meta::NAME . '_' . Ctas_Meta::MEMBERSHIP_LINK, 'option' );

		if ( ! $membership_url ) {
			return null;
		}

		return sanitize_url( $membership_url );
	}

	public function get_primary_category_from_id( int|null $post_id ): ?object {
		if ( ! $post_id ) {
			return null;
		}

		$categories = get_the_terms( $post_id, 'category' );

		if ( function_exists( 'yoast_get_primary_term' ) ) {
			if ( $primary_category_id = yoast_get_primary_term_id( 'category', $post_id ) ) {
				$categories = [ get_term( $primary_category_id, 'category' ) ];
			}
		}

		return ( is_array( $categories ) && count( $categories ) >= 1 )
			? (object) [
				'link'  => get_term_link( $categories[0] ),
				'label' => $categories[0]->name,
			]
			: null;
	}

	public function get_render_gallery_list() {
		return count( $this->get_content_args() ) <= 1;
	}
}
