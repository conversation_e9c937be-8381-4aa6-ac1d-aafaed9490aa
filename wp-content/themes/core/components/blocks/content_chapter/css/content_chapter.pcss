/* -----------------------------------------------------------------------------
 *
 * Block: Content Chapter
 *
 * ----------------------------------------------------------------------------- */

.b-content-chapter {
	width: 100% !important;
	max-width: 100% !important;

	&__header {
		height: 100vh;
		max-height: 1080px;
		position: relative;

		.l-container {
			padding-top: var(--spacer-40);
			padding-bottom: var(--spacer-40);

			@media (--viewport-full) {
				padding-top: var(--spacer-80);
			}
		}

		&:before {
			content: "";
			display: block;
			position: absolute;
			width: 100%;
			height: 75%;
			z-index: 2;
			opacity: 0.8;

			background: var(--color-white);
			background: linear-gradient(180deg, var(--color-white) 25%, rgba(0, 0, 0, 0) 60%);
		}

		&.has-dark-background-color:before {
			background: var(--color-background-dark);
			background: linear-gradient(180deg, var(--color-background-dark) -25%, rgba(0, 0, 0, 0) 60%);
		}

		&_image {
			z-index: 1;

			&-img {
				position: absolute;
				background-size: cover;
				background-position-x: center;
				background-position-y: top;
				width: 100%;
				height: 100%;
			}
		}
	}

	&__media-container {
		position: absolute;
		background-size: cover;
		background-position-x: center;
		background-position-y: top;
		width: 100%;
		height: 100%;
		overflow: hidden;
		display: flex;
		justify-content: center;
		align-items: center;

		.b-content-chapter__lottie-player {
			transform: scale( var( --lottie-scale, 1 ) );
			transform-origin: center;
		}

		.c-lottie-player,
		.c-lottie-player__player {
			height: 100%;

			.c-lottie-player__animation {
				border-radius: 0;
			}
		}
	}

	&__container {
		display: grid;
		grid-column-gap: 0;
		grid-template-columns: 1fr;

		@media (--viewport-large) {
			grid-column-gap: var(--grid-gutter);
			grid-template-columns: var(--grid-3-col) var(--grid-1-col) var(--grid-8-col);
		}
	}

	&__sidebar {
		margin-top: var(--spacer-80);
		margin-bottom: var(--spacer-80);

		display: none;

		@media (--viewport-large) {
			display: grid;
			grid-template-rows: 80vh 20vh;
		}

		&-container {
			--header-size: 200px;

			position: sticky;
			top: var(--header-size);
			height: calc(100vh - var(--header-size));

			display: grid;
			grid-template-rows: 1fr auto;
		}

		&__nav-header,
		&__footer-newsletter,
		&__footer-share-title {
			@mixin t-body-small;
			font-size: var(--font-size-body-xsmall);
			line-height: 1.67;
			color: var(--color-neutral-50);
			margin-bottom: var(--spacer-30);
		}

		&__nav {
			overflow-y: auto;
			padding-right: var(--spacer-20);

			@mixin scrollbar-neutral;
		}

		&__footer {
			margin-top: var(--spacer-30);
			margin-bottom: var(--spacer-30);

			&-share {
				display: flex;
				column-gap: var(--spacer-10);
				align-items: center;

				&-title {
					margin-bottom: unset;
				}

				a&-link {
					font-size: 24px;
					line-height: 1;

					.icon {
						color: var(--color-neutral-30);

						transition: var(--transition);
					}

					&:hover,
					&:focus,
					&:active {
						text-decoration: none;

						.icon {
							color: var(--color-primary);
						}
					}
				}
			}
		}

		&-list {
			counter-reset: list-counter 0;

			display: flex;
			flex-direction: column;
			row-gap: var(--spacer-20);
		}

		a&-link {
		$counter-width: 28 px;

			display: grid;
			align-items: center;
			grid-template-columns: $counter-width 1fr;
			grid-column-gap: 10px;
			column-gap: var(--spacer-10);

			span {
				transition: var(--transition);

				color: var(--color-text);
				font-weight: var(--font-weight-regular);
			}

			&:before {
				counter-increment: list-counter;
				content: counter(list-counter);
				display: flex;
				align-items: center;
				justify-content: center;

				border: 2px solid var(--color-text);
				border-radius: var(--border-radius-highlights-small);

				width: $counter-width;
				height: var(--spacer-30);

				color: var(--color-text);
				box-sizing: border-box;

				font-weight: var(--font-weight-semibold);
			}

			&:hover,
			&:focus,
			&:active {
				text-decoration: none;

				span {
					text-decoration: underline;
					color: var(--color-primary-dark);
				}
			}

			&--current {
				span {
					font-weight: var(--font-weight-semibold);
				}

				&:before {
					border-color: var(--color-secondary);
					background-color: var(--color-secondary);
				}
			}
		}
	}

	&__content {
		margin-top: var(--spacer-50);
		margin-bottom: var(--spacer-50);

		@media (--viewport-large) {
			margin-top: var(--spacer-80);
			margin-bottom: var(--spacer-80);
		}

		> * {
			@media (--viewport-small-max) {
				width: 100% !important;
			}
		}
	}

	h2&__title {
		position: relative;
		z-index: 5;
		max-width: var(--grid-width-staggered-double);
		margin: 0 auto;

		color: var(--color-text);
		text-align: center;
		font-weight: var(--font-weight-semibold);

		display: flex;
		flex-direction: column;
		row-gap: var(--spacer-20);
		align-items: center;
		justify-content: center;

		@mixin t-display-x-small;

		@media (--viewport-full) {
			@mixin t-display;
		}

		.b-content-chapter__header.has-dark-background-color & {
			color: var(--color-white);
		}

		&:before {
			counter-increment: chapter-counter;
			content: counter(chapter-counter);

			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;

			border: 3px solid var(--color-secondary);
			border-radius: var(--border-radius-highlights);

			min-width: var(--spacer-30);
			min-height: var(--spacer-30);
			padding: 2px;

			@mixin t-display-xxx-small;

			@media (--viewport-full) {
				min-width: var(--spacer-40);
				min-height: var(--spacer-40);

				@mixin t-display-x-small;
			}
		}
	}

}
