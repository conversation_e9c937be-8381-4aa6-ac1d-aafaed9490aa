<?php
declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\blocks\content_chapter;

use Tribe\Libs\Utils\Markup_Utils;
use Tribe\Project\Object_Meta\Post_Meta;
use Tribe\Project\Object_Meta\Post_Template_Editorial_Meta;
use Tribe\Project\Post_Types\Post\Post_Template_Editorial;
use Tribe\Project\Templates\Components\Abstract_Controller;
use Tribe\Project\Templates\Components\container\Container_Controller;
use Tribe\Project\Templates\Components\Deferred_Component;
use Tribe\Project\Templates\Components\image\Image_Controller;
use Tribe\Project\Templates\Components\link\Link_Controller;
use Tribe\Project\Blocks\Types\Content_Chapter\Content_Chapter as Content_Chapter_Block;
use Tribe\Project\Templates\Components\lottie_player\Lottie_Player_Controller;
use Tribe\Project\Templates\Components\Traits\With_Lottie_Data;

class Content_Chapter_Block_Controller extends Abstract_Controller {
	use With_Lottie_Data;

	public const HEADER_IMAGE           = 'header_image';
	public const HEADER_DARK_BACKGROUND = 'header_dark_background';
	public const CHAPTER_TITLE          = 'chapter_title';
	public const CONTAINER_CLASSES      = 'container_classes';
	public const CLASSES                = 'classes';
	public const ATTRS                  = 'attrs';
	public const CLASS_BASE             = 'b-content-chapter';
	public const MEDIA_TYPE             = 'media_type';
	public const LOTTIE_FILE_HEIGHT     = 'lottie_file_height';
	public const LOTTIE_FILE_WIDTH      = 'lottie_file_width';

	private int    $header_image;
	private bool   $header_dark_background;
	private string $chapter_title;
	private array  $container_classes;
	private array  $classes;
	private array  $attrs;
	private string $media_type;
	private string $lottie_file_height;
	private string $lottie_file_width;

	/**
	 * @param array $args
	 */
	public function __construct( array $args = [] ) {
		$args = $this->parse_args( $args );

		$this->classes                = (array) $args[ self::CLASSES ];
		$this->header_image           = $args[ self::HEADER_IMAGE ];
		$this->header_dark_background = (bool) $args[ self::HEADER_DARK_BACKGROUND ];
		$this->chapter_title          = (string) $args[ self::CHAPTER_TITLE ];
		$this->container_classes      = (array) $args[ self::CONTAINER_CLASSES ];
		$this->attrs                  = (array) $args[ self::ATTRS ];
		$this->media_type             = (string) $args[ self::MEDIA_TYPE ];
		$this->lottie_file_height     = (string) $args[ self::LOTTIE_FILE_HEIGHT ];
		$this->lottie_file_width      = (string) $args[ self::LOTTIE_FILE_WIDTH ];

		$this->fill_lottie_data( $args );

		if ( $this->media_type === Content_Chapter_Block::LOTTIE && ! $this->get_lottie_json_url() ) {
			$this->media_type = Content_Chapter_Block::IMAGE;
		}
	}

	/**
	 * @return array
	 */
	protected function defaults(): array {
		return [
			self::HEADER_IMAGE       => 0,
			self::CHAPTER_TITLE      => '',
			self::CONTAINER_CLASSES  => [],
			self::CLASSES            => [],
			self::ATTRS              => [],
			self::MEDIA_TYPE         => '',
			self::LOTTIE_DATA        => [],
			self::LOTTIE_JSON_URL    => '',
			self::LOTTIE_FILE_HEIGHT => '',
			self::LOTTIE_FILE_WIDTH  => '',
		];
	}

	/**
	 * @return array
	 */
	protected function required(): array {
		return [
			self::CONTAINER_CLASSES => [ 'l-container' ],
			// Note: This block does not use `c-block` intentionally.
			self::CLASSES           => [ self::CLASS_BASE ],
		];
	}

	/**
	 * @return string
	 */
	public function get_classes(): string {
		return Markup_Utils::class_attribute( $this->classes );
	}

	/**
	 * @return string
	 */
	public function get_attrs(): string {
		return Markup_Utils::concat_attrs( $this->attrs );
	}

	/**
	 * @param array $additional_classes
	 *
	 * @return string
	 */
	public function get_container_classes( array $additional_classes = [] ): string {
		return Markup_Utils::class_attribute( array_merge( $this->container_classes, $additional_classes ) );
	}

	public function append_class_name( $class ): string {
		return self::CLASS_BASE . '__' . $class;
	}

	/**
	 * @return string
	 */
	public function get_chapter_title(): string {
		return $this->chapter_title ?? '';
	}

	/**
	 * @return bool
	 */
	public function get_header_has_dark_background(): bool {
		return $this->header_dark_background;
	}

	/**
	 * @return string
	 */
	public function get_chapter_chapter_id(): string {
		return sanitize_title( $this->chapter_title ) ?? '';
	}

	/**
	 * @return bool
	 */
	public function is_editorial_post(): bool {
		return get_page_template_slug() === Post_Template_Editorial::TEMPLATE_FILE;
	}

	/**
	 * @return string
	 */
	public function get_meta_table_contents_title(): string {
		$sidebar_group = get_field( Post_Template_Editorial_Meta::CHAPTER_SIDEBAR, get_the_ID() );

		if ( ! $sidebar_group ) {
			return Post_Template_Editorial_Meta::TABLE_CONTENTS_TITLE_DEFAULT;
		}

		$title = $sidebar_group[ Post_Template_Editorial_Meta::TABLE_CONTENTS_TITLE ];

		return ! empty( $title ) ? $title : Post_Template_Editorial_Meta::TABLE_CONTENTS_TITLE_DEFAULT;
	}

	/**
	 * @return bool
	 */
	public function get_meta_hide_newsletter(): bool {
		$sidebar_group = get_field( Post_Template_Editorial_Meta::CHAPTER_SIDEBAR, get_the_ID() );

		if ( ! $sidebar_group ) {
			return false;
		}

		return (bool) $sidebar_group[ Post_Template_Editorial_Meta::HIDE_NEWSLETTER ];
	}

	/**
	 * @return string|null
	 */
	public function get_meta_newsletter_url(): ?string {
		$newsletter_url = get_field( Post_Template_Editorial_Meta::NEWSLETTER_URL, get_the_ID() );

		if ( ! $newsletter_url ) {
			return null;
		}

		return sanitize_url( $newsletter_url );
	}

	/**
	 * @return Deferred_Component
	 */
	public function get_header_image(): Deferred_Component {

		$img = wp_get_attachment_image_url( $this->header_image, [ 1920, 1080 ] );

		return defer_template_part( 'components/image/image', null, [
			Image_Controller::IMG_URL      => esc_url( $img ),
			Image_Controller::AS_BG        => true,
			Image_Controller::USE_LAZYLOAD => true,
			Image_Controller::CLASSES      => [ $this->append_class_name( 'header_image' ) ],
			Image_Controller::IMG_ALT_TEXT => esc_attr( $this->get_chapter_title() ),
			Image_Controller::IMG_CLASSES  => [ $this->append_class_name( 'header_image-img' ) ],
		] );
	}

	/**
	 * @return Deferred_Component|null
	 */
	public function get_header_media(): ?Deferred_Component {
		if ( $this->get_media_type() === Content_Chapter_Block::IMAGE ) {
			return $this->get_header_image();
		} elseif ( $this->get_media_type() === Content_Chapter_Block::LOTTIE && ! empty( $this->get_lottie_player_args() ) ) {
			$lottie = defer_template_part(
				'components/lottie_player/lottie_player',
				null,
				array_merge(
					$this->get_lottie_player_args(),
					[
						Lottie_Player_Controller::CLASSES => [
							$this->append_class_name( 'lottie-player' ),
						],
						Lottie_Player_Controller::ATTRS   => [
							"data-width" => (int) esc_attr( $this->lottie_file_width ),
							"data-height" => (int) esc_attr( $this->lottie_file_height ),
							"style" => 'width: ' . esc_attr( $this->lottie_file_width ) . '; height: ' . esc_attr( $this->lottie_file_height ) . ';',
						],
					]
				)
			);

			return defer_template_part( 'components/container/container', null, [
				Container_Controller::CLASSES => [ self::CLASS_BASE . '__media-container' ],
				Container_Controller::CONTENT => $lottie,
			] );
		}

		return null;
	}

	public function get_media_type(): string {
		return $this->media_type;
	}

	/**
	 * @param $social_network
	 *
	 * @return Deferred_Component|null
	 */
	public function get_social_share_icon( $social_network ): ?Deferred_Component {
		if ( ! get_permalink() ) {
			return null;
		}

		$know_social_networks = [
			'facebook'  => [
				'link' => 'https://www.facebook.com/sharer.php?u=',
				'name' => 'Facebook',
			],
			'twitter'   => [
				'link' => 'https://twitter.com/share?url=',
				'name' => 'Twitter',
			],
			'pinterest' => [
				'link' => 'https://pinterest.com/pin/create/bookmarklet/?url=',
				'name' => 'Pinterest',
			],
			'linkedin'  => [
				'link' => 'https://www.linkedin.com/shareArticle?url=',
				'name' => 'LinkedIn',
			],
		];

		if ( ! array_key_exists( $social_network, $know_social_networks ) ) {
			return null;
		}

		[ 'link' => $social_link, 'name' => $social_name ] = $know_social_networks[ $social_network ];

		return defer_template_part( 'components/link/link', null, [
			Link_Controller::URL        => $social_link . urlencode( get_permalink() ),
			Link_Controller::TARGET     => '_blank',
			Link_Controller::CLASSES    => [ 'b-content-chapter__sidebar__footer-share-link' ],
			Link_Controller::CONTENT    => "<i class='icon icon-$social_network'></i>",
			Link_Controller::ARIA_LABEL => sprintf( __( 'Share on %s', 'tribe' ), $social_name ),
		] );
	}

	/**
	 * @param $current_id
	 *
	 * @return array
	 */
	public function get_sidebar_links( $current_id ): array {
		$links   = get_field( Post_Meta::SECTION_JUMP_LINKS, get_the_ID() );
		$content = '';

		if ( ! is_array( $links ) ) {
			return [];
		}

		foreach ( $links as $link ) {
			[ Post_Meta::BLOCK_ID => $block_id, Post_Meta::LABEL => $label ] = $link;

			$link_classes = [ 'b-content-chapter__sidebar-link' ];

			if ( $block_id === $current_id ) {
				$link_classes[] = 'b-content-chapter__sidebar-link--current';
			}

			$content .= defer_template_part( 'components/link/link', null, [
				Link_Controller::URL        => "#$block_id",
				Link_Controller::CLASSES    => $link_classes,
				Link_Controller::CONTENT    => "<span>$label</span>",
				Link_Controller::ARIA_LABEL => sprintf( __( 'Go to %s', 'tribe' ), $label ),
			] )->render();
		}

		return [
			Container_Controller::CLASSES => [ 'b-content-chapter__sidebar-list' ],
			Container_Controller::CONTENT => $content,
		];
	}
}
