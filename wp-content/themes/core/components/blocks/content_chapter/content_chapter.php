<?php
declare( strict_types=1 );

use Tribe\Project\Templates\Components\blocks\content_chapter\Content_Chapter_Block_Controller;

/**
 * @var array $args Arguments passed to the template
 */
// phpcs:ignore VariableAnalysis.CodeAnalysis.VariableAnalysis.UndefinedVariable
$c = Content_Chapter_Block_Controller::factory( $args );

if ( ! $c->is_editorial_post() && ! current_user_can( 'manage_options' ) ) {
	return;
}

$conditional_styles = '';

if ( $c->get_header_has_dark_background() ) {
	$conditional_styles .= ' has-dark-background-color';
}

$conditional_styles .= ' b-content-chapter__header--' . $c->get_media_type();
?>

<section <?php echo $c->get_classes(); ?> <?php echo $c->get_attrs(); ?>
		id="<?php echo $c->get_chapter_chapter_id(); ?>">
	<div>
		<header class="b-content-chapter__header<?php echo $conditional_styles; ?>">
			<?php echo $c->get_header_media(); ?>
			<div <?php echo $c->get_container_classes(); ?>>
				<h2 class="b-content-chapter__title"><?php echo $c->get_chapter_title(); ?></h2>
			</div>
		</header>
		<div <?php echo $c->get_container_classes( [
				$c->append_class_name( 'container' ),
		] ); ?>>
			<aside class="b-content-chapter__sidebar">
				<div class="b-content-chapter__sidebar-container">
					<nav class="b-content-chapter__sidebar__nav">
						<header class="b-content-chapter__sidebar__nav-header"><?php echo $c->get_meta_table_contents_title(); ?></header>
						<?php get_template_part( 'components/container/container', null, $c->get_sidebar_links( $c->get_chapter_chapter_id() ) ); ?>
					</nav>
					<footer class="b-content-chapter__sidebar__footer">
						<?php if ( ! $c->get_meta_hide_newsletter() ) : ?>
							<div class="b-content-chapter__sidebar__footer-newsletter">
								<span>Love this content? <a href="<?php echo $c->get_meta_newsletter_url(); ?>">Join our newsletter</a> to get content like this straight into your inbox.</span>
							</div>
						<?php endif; ?>
						<div class="b-content-chapter__sidebar__footer-share">
							<span class="b-content-chapter__sidebar__footer-share-title">
								Share
							</span>
							<?php echo $c->get_social_share_icon( 'twitter' ); ?>
							<?php echo $c->get_social_share_icon( 'facebook' ); ?>
							<?php echo $c->get_social_share_icon( 'pinterest' ); ?>
							<?php echo $c->get_social_share_icon( 'linkedin' ); ?>
						</div>
					</footer>
				</div>
			</aside>
			<div class="b-content-chapter__empty-col"></div>
			<main class="b-content-chapter__content">
				<InnerBlocks class="s-sink t-sink l-sink" />
			</main>
		</div>
	</div>
</section>
