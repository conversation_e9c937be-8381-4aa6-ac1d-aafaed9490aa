/* -----------------------------------------------------------------------------
 *
 * Component: Content Chapter Block
 *
 * ----------------------------------------------------------------------------- */

/**
 * @module
 * @description Javascript that drives the Content Chapter block
 */

import * as tools from 'utils/tools';
import { on } from 'utils/events';
import state from 'config/state';

const contentChapterBlock = tools.getNodes( '.b-content-chapter', true, document, true )[ 0 ];
const component = {
	topMargin: 0, // some buffer to make sure we are offset from the header nicely
};

/**
 * Set the sidebar position based upon the height of all of our sticky header elements
 */
const chapterSidebarTopPosition = () => {
	const mastheadWrap = tools.getNodes( 'masthead' )[ 0 ];
	const progressBar = tools.getNodes( '.progress-bar', true, document, true )[ 0 ];
	const navigationBar = tools.getNodes( '.post-navigation-bar', true, document, true )[ 0 ];
	const stickySidebars = tools.getNodes( '.b-content-chapter__sidebar-container', true, document, true );

	// Calculate the header height + admin bar + quick links
	const adminBarHeight = state.admin_bar_height;
	const mastheadHeight = mastheadWrap ? mastheadWrap.clientHeight : 0;
	const progressBarHeight = progressBar ? progressBar.clientHeight : 0;
	const navigationBarHeight = navigationBar ? navigationBar.clientHeight : 0;

	// Loop through each ad's sidebar
	stickySidebars.forEach( sidebar => {
		// Set top position of sidebar to ensure it displays below header area
		sidebar.style.setProperty( '--header-size', `${
			adminBarHeight +
			mastheadHeight +
			progressBarHeight +
			navigationBarHeight +
			component.topMargin }px` );

		const current = sidebar.querySelector( '.b-content-chapter__sidebar-link--current' );

		if ( current ) {
			current
				.closest( '.b-content-chapter__sidebar__nav' )
				.scrollTo( {
					top: sidebar.querySelector( '.b-content-chapter__sidebar-link--current' ).offsetTop - 44,
					behavior: 'smooth',
				} );
		}
	} );
};

const updateChapterLottieScales = () => {
	const chapterLotties = tools.getNodes( '.b-content-chapter__lottie-player', true, document, true );

	if ( ! chapterLotties || ! chapterLotties.length ) {
		return;
	}

	for ( const lottie of chapterLotties ) {
		const fileWidth = lottie.getAttribute( 'data-width' );
		const fileHeight = lottie.getAttribute( 'data-height' );
		const viewportWidth = state.v_width;
		const viewportHeight = state.v_height;

		if ( fileWidth && fileHeight && viewportWidth && viewportHeight ) {
			const scale = Math.max( viewportWidth / fileWidth, viewportHeight / fileHeight );
			lottie.style.setProperty( '--lottie-scale', scale + 0.1 ); // Add a small buffer to ensure it fits well
		}
	}
};

/**
 * @function handleResize
 * @description Handle Browser Resize Events
 */
const handleResize = () => {
	if ( state.is_desktop ) {
		chapterSidebarTopPosition();
	}

	updateChapterLottieScales();
};

/**
 * @function bindEvents
 * @description Bind the events for this module here.
 */
const bindEvents = () => {
	on( document, 'modern_tribe/resize_executed', handleResize );
};

/**
 * @function init
 * @description Initializes the class if the element(s) to work on are found.
 */
const init = () => {
	if ( ! contentChapterBlock ) {
		return;
	}

	// The sidebar is only sticky on desktop
	if ( state.is_desktop ) {
		chapterSidebarTopPosition();
	}

	updateChapterLottieScales();

	bindEvents();

	console.info( 'SquareOne Theme: Initialized Content Chapter scripts.' );
};

export default init;
