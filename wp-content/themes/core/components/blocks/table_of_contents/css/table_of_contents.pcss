/* -----------------------------------------------------------------------------
 *
 * Table of Contents
 *
 * ----------------------------------------------------------------------------- */

.b-table-of-contents {
	margin-top: var(--spacer-50);
	margin-bottom: var(--spacer-50);

	&:has(.b-table-of-contents__list--small-blue-links) {
		.b-table-of-contents__title {
			margin-bottom: var(--spacer-10);
			color: var(--color-neutral-50);

			@mixin t-overline;
		}
	}

	&:has(.b-table-of-contents__list--black-links-underlined) {
		counter-reset: toc-black-links-counter 0;
	}

	&:has(.b-table-of-contents__list--numbered-list) {
		counter-reset: toc-numbered-list-counter 0;
	}

	&__title {
		margin-top: var(--spacer-60);
		margin-bottom: var(--spacer-20);
	}

	&__list {
		overflow: hidden;
		max-height: 3000px;
		transition: all 300ms;

		&-item {
			margin-bottom: var(--spacer-10);
		}

		/*VARIATION: Small blue links with divider*/

		&--small-blue-links {
			padding-left: 0 !important;

			.b-table-of-contents__link {
				@mixin t-body-small;

				font-weight: var(--font-weight-bold);
				text-decoration: none !important;

				&:hover,
				&:focus {
					color: var(--global-color-primary-hover);
					text-decoration: underline !important;
					text-decoration-color: inherit;
				}

				@media (--viewport-small-max) {
					display: inline-block;

					&:before {
						@mixin separators-dot;
						display: inline-block;
						position: relative;
						margin-right: 5px;
						right: 0;
					}

					&:not(:last-of-type) {
						padding-bottom: var(--spacer-10);
					}

					&:last-of-type {
						padding-bottom: var(--spacer-30);
					}
				}
			}

			.b-table-of-contents__separator {
				@media (--viewport-medium) {
					padding: 0 var(--spacer-10);

					&::after {
						@mixin separators-dot;
						right: 0;
						top: 0;
						display: inline-block !important;
						position: relative;
					}
				}
			}
		}

		/*VARIATION: Black links w/accent underline*/

		&--black-links-underlined {
			list-style: none !important;
			padding-left: 0 !important;

			@media (--viewport-full) {
				column-count: 2;
			}

			.b-table-of-contents__list-item {
				display: grid;
				grid-template-columns: var(--spacer-30) 1fr;
				grid-column-gap: var(--spacer-10);
				margin-bottom: var(--spacer-30);

				&:before {
					counter-increment: toc-black-links-counter;
					content: counter(toc-black-links-counter) ".";
					display: block;
					text-align: right;

					@mixin t-body;
					font-weight: var(--font-weight-bold);
				}

				.b-table-of-contents__link {
					@mixin t-body;

					color: var(--color-text);
					text-decoration: none !important;

					font-weight: var(--font-weight-bold);
					transition: var(--transition);

					border-bottom: 4px solid var(--color-secondary);

					&:hover,
					&:focus,
					&:active {
						border-width: 8px;
						color: var(--color-text);
					}
				}
			}
		}

		/*VARIATION: Numbered List (boxed numbers)*/

		&--numbered-list {
			list-style: none !important;
			padding-left: 0 !important;

			display: inline-flex;
			flex-direction: column;
			gap: var(--spacer-20);

			.b-table-of-contents__list-item {
				.b-table-of-contents__link {
					display: inline-grid;
					grid-auto-flow: column;
					gap: 12px;
					align-items: center;
					text-decoration: none !important;
					color: var(--color-text);

					@mixin t-body-small;

					&:before {
						@mixin t-body-small;

						display: flex;
						justify-content: center;
						align-items: center;
						width: 28px;
						height: 28px;
						border: 2px solid var(--color-background-dark);
						color: var(--color-background-dark);
						text-align: center;
						border-radius: var(--border-radius-highlights-small);

						counter-increment: toc-numbered-list-counter;
						content: counter(toc-numbered-list-counter);
					}

					&:hover,
					&:focus {
						font-weight: var(--font-weight-semibold);

						&:before {
							background-color: var(--color-secondary-50);
							border-color: var(--color-secondary-50);
						}
					}
				}
			}
		}

		/* LIST OF TOOLS */

		&-tools {
			counter-reset: toc-tools-links-counter 0;

			&-item {
				margin-bottom: var(--spacer-10);
				list-style: circle outside;
				margin-left: 1.2em;
			}
		}

		&--small-blue-links {
			.b-table-of-contents__list-tools {
				&-item {
					list-style: none;
				}
			}
		}

		&--black-links-underlined {
			.b-table-of-contents__list-tools {
				&-controller {
					display: none;
				}

				&-item {
					list-style: none;

					&:before {
						counter-increment: toc-tools-links-counter;
						content: counter(toc-tools-links-counter, lower-alpha) ".";
					}
				}
			}
		}

		&--numbered-list {
			.b-table-of-contents__list-tools {
				display: inline-flex;
				flex-direction: column;
				grid-gap: var(--spacer-20);
				gap: var(--spacer-20);

				&-item {
					list-style: none;

					&:before {
						display: none;
					}

					.b-table-of-contents__link {
						&:before {
							counter-increment: toc-tools-links-counter;
							content: counter(toc-tools-links-counter, lower-alpha) ".";
						}
					}
				}
			}
		}
	}


	/* HIDE BULLETS */

	&--hide-bullets {
		.b-table-of-contents__list {
			list-style: none;
			padding-left: 0;
		}

		.b-table-of-contents__list--black-links-underlined {
			.b-table-of-contents__list-item {
				display: block;

				&:before {
					content: none;
				}
			}
		}

		.b-table-of-contents__list--numbered-list {
			.b-table-of-contents__list-item {
				.b-table-of-contents__link {
					display: block;

					&:before {
						content: none;
					}
				}
			}
		}

		.b-table-of-contents__list--small-blue-links {
			.b-table-of-contents__separator {
				display: none;
			}
		}
	}

	&--sticky-header {
		padding: 0;
		width: 100%;
		max-width: 100%;
		border: solid 1px var(--color-neutral-20);
		border-radius: var(--border-radius-base);

		@media (--viewport-medium) {
			border: none;
			border-radius: 0;
		}

		.l-sink:not(.item-single__regwall-protected) > &:not(.c-block):not(.alignwide):not(.alignfull):not(.wp-block-group):not(.item-single__regwall-protected) {
			width: 100%;
			max-width: 100%;

			@media (--viewport-medium) {
				max-width: var(--grid-width-staggered-double);
			}
		}
	}

	&--expanded &__container {
		max-height: 1000px;
		transition: max-height 1s, margin-top 1s, margin-bottom 1s, border 1s;

		@media (--viewport-medium) {
			transition: none;
		}
	}

	&--sticky-header &__list {
		list-style: inside;
		padding: var(--spacer-30);
		padding-top: 0;
		transition: padding 300ms;

		@media (--viewport-medium) {
			padding: var(--spacer-30) 0 0 0;
		}

		.b-table-of-contents__list-tools {
			padding-left: var(--spacer-30);

			&-item {
				list-style: decimal;
				padding-left: var(--spacer-10);
			}
		}
	}

	&--sticky-header &__container {
		max-height: 0;
		padding-bottom: 0;
		overflow: hidden;
		transition: max-height 500ms, margin-top 500ms, margin-bottom 500ms, border 500ms;
		width: 100%;
		@mixin scrollbar-neutral;

		@media (--viewport-medium) {
			max-height: 1000px;
			transition: none;
		}
	}

	&--expanded &__container {
		max-height: 1000px;
		transition: max-height 1s, margin-top 1s, margin-bottom 1s, border 1s;

		@media (--viewport-medium) {
			transition: none;
		}
	}

	&--sticky-header &__title {
		display: block;
		margin: 0;
		padding: var(--spacer-20);
		cursor: pointer;
		color: var(--color-neutral-50);
		position: relative;

		@mixin t-overline;

		@media (--viewport-medium) {
			border: none;
			padding: 0;
			cursor: initial;
		}

		&::after {
			display: block;
			position: absolute;
			content: var(--icon-chevron-down);
			font-family: var(--font-family-core-icons);
			font-size: var(--font-size-body-large);
			top: 50%;
			transform: translateY(-50%);
			right: var(--spacer-20);
			transition: transform 300ms;

			@media (--viewport-medium) {
				display: none;
			}
		}
	}

	&--locked &__title {
		padding: var(--spacer-20);
	}

	&--locked {
		background-color: var(--color-white);
		z-index: 8;
		position: fixed;
		padding: var(--spacer-20) var(--spacer-30);
		border-top: 0;
		border-left: 0;
		border-right: 0;
		border-radius: 0;
		border-bottom: solid 1px var(--color-neutral-20);
		margin: 0;
		top: var(--toc-sticky-top);

		.l-sink:not(.item-single__regwall-protected) > &:not(.c-block):not(.alignwide):not(.alignfull):not(.wp-block-group):not(.item-single__regwall-protected) {
			width: 100vw;
			max-width: 100vw;
			margin-left: calc(var(--grid-margin-small) * -1);

			@media (--viewport-medium) {
				max-width: 500px;
				margin-left: 0;
			}
		}

		@media (--viewport-medium) {
			padding: 0;
			left: 50%;
			transform: translateX(-50%);
			background-color: var(--color-neutral-10);
			border: solid 1px var(--color-neutral-20);
			border-radius: var(--border-radius-base);
			margin-top: var(--spacer-20);
			box-shadow: var(--box-shadow-10);
		}
	}

	&--locked &__title {
		padding: var(--spacer-20);
		border: solid 1px var(--color-neutral-20);
		border-radius: var(--border-radius-base);
		margin-bottom: 0;
		position: relative;

		&::after {
			display: block;
		}

		@media (--viewport-medium) {
			border: none;
			border-radius: 0;
			cursor: pointer;
			padding: var(--spacer-20);
		}

		&[data-active-text]::before {
			@media (--viewport-medium) {
				content: '\2022' '\2009' ' ' attr(data-active-text);
				position: absolute;
				font-size: var(--font-size-body-small);
				left: 45%;
				width: 45%;
				top: 50%;
				transform: translateY(-50%);
				font-weight: var(--font-weight-medium);
				text-transform: none;
				letter-spacing: 0;
				text-overflow: ellipsis;
				white-space: nowrap;
				overflow: hidden;
			}
		}

		&::after {
			@media (--viewport-medium) {
				display: block;
			}
		}

	}

	&--expanded &__title::after {
		transform: translateY(-50%) rotate(180deg);
	}

	&--locked &__container {
		overflow-y: scroll;
		padding-left: 0;
		padding-bottom: 0;
		transition: none;
		@mixin scrollbar-neutral-darker;
		@mixin scrollbar-transparent-padding;

		ol {
			padding: 0;
		}

		@media (--viewport-medium) {
			margin: 0;
			width: 100%;
			border-bottom: solid 1px transparent;
			max-height: 0;
		}
	}

	&--locked &__scroll-container {
		position: relative;

		@media (--viewport-medium) {
			margin: 0 var(--spacer-30);
		}

		&::before,
		&::after {
			content: '';
			display: block;
			width: calc(100% - var(--spacer-20));
			height: 20px;
			opacity: 0;
			transition: opacity 300ms;
			left: 0;
			position: absolute;
		}

		&::before {
			top: 0;
			background: rgb(255,255,255);
			background: linear-gradient(0deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.6) 25%, rgba(255,255,255,1) 53%);
		}

		&::after {
			bottom: 0;
			background: rgb(255,255,255);
			background: linear-gradient(180deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.6) 25%, rgba(255,255,255,1) 53%);
		}

		@media (--viewport-medium) {
			&::before {
				background: rgb(247,249,251);
				background: linear-gradient(0deg, rgba(247,249,251,0) 0%, rgba(247,249,251,0.6) 25%, rgba(247,249,251,1) 53%);
			}

			&::after {
				background: rgb(247,249,251);
				background: linear-gradient(180deg, rgba(247,249,251,0) 0%, rgba(247,249,251,0.6) 25%, rgba(247,249,251,1) 53%);
			}
		}
	}

	&--expanded &__scroll-container {
		&::before,
		&::after {
			opacity: 1;
		}
	}

	&--expanded.b-table-of-contents--locked {
		padding: var(--spacer-20) var(--spacer-30) 0 var(--spacer-30);

		@media (--viewport-medium) {
			padding: 0;
		}
	}

	&--locked &__list {
		padding: 0;

		@media (--viewport-medium) {
			padding-top: 0;
		}
	}

	&--locked .t-sink &__container li {
		&:not(.active) a {
			font-weight: var(--font-weight-regular);
			color: var(--color-black);
			text-decoration: none;

			&.active {
				font-weight: var(--font-weight-semibold);
				color: var(--color-primary);
			}
		}
	}

	&--expanded.b-table-of-contents--locked &__container {
		max-height: 180px;
		padding: 0;
		width: 100%;

		@media (--viewport-medium) {
			max-height: 180px;
		}

		@media (--viewport-medium) {
			margin: 0;
		}
	}

	&--expanded.b-table-of-contents--locked &__list {
		padding: var(--spacer-20) 0;
	}

	&--expanded.b-table-of-contents--locked &__title {
		border-bottom: solid 1px var(--color-neutral-20);
	}

	&--transition &__container {
		transition: max-height 500ms, margin 500ms, border 500ms;
	}
}

/* override perfect listicle layout */
.item-single--perfect-listicle-layout .b-table-of-contents,
.item-single--tool .b-table-of-contents {
	&__title {
		@mixin t-overline;

		display: block;
		margin-top: var(--spacer-50);
		margin-bottom: var(--spacer-10);
		color: var(--color-neutral-30);
	}
}


/* Table Of Contents on mobile in Licticles */
.item-single--perfect-listicle-layout,
.item-single--tool {
	@media (--viewport-small-max) {
		.b-table-of-contents {
			padding-left: var(--spacer-30);
			padding-right: var(--spacer-30);
			border: 1px solid var(--color-neutral-20);
			border-radius: var(--border-radius-highlights-small);

			&__title {
				cursor: pointer;
				position: relative;
				margin: 0;
				padding: var(--spacer-30) var(--spacer-30) var(--spacer-30) 0;
				color: var(--color-neutral-50);

				&:before {
					@mixin icon;

					align-items: center;
					border: 2px solid transparent;
					border-radius: 50%;
					position: absolute;
					right: 0;
					display: flex;
					float: left;
					height: 20px;
					color: var(--color-black);
					content: var(--icon-chevron-down);
					font-family: var(--font-family-core-icons);
					font-size: var(--font-size-body-large);
					line-height: 1.8;
					justify-content: center;
					text-align: center;
					transition: 0.25s ease-in-out;
					width: 20px;
					top: 50%;
					transform: translateY(-50%);
				}
			}

			&__containers {
				position: relative;
				max-height: 100px;
				padding-left: var(--spacer-20);
				overflow: hidden;
				transition: max-height 0.2s ease-out;

				&:after {
					position: absolute;
					content: "";
					width: 100%;
					height: 100%;
					bottom: 0;
					left: 0;
					background: transparent linear-gradient(180deg, #FFFFFF00 0%, #FFFFFF 100%) 0% 0% no-repeat padding-box;
					transition: height 0.4s ease-in-out;
				}

				.b-table-of-contents__list-item {
					&:last-of-type {
						margin-bottom: var(--spacer-30);
					}
				}

				.b-table-of-contents__list-tools-item {
					&:last-of-type {
						margin-bottom: var(--spacer-10);
					}
				}
			}

			&__link {
				text-decoration: none !important;
			}

			&.active {
				.b-table-of-contents__title {
					&:before {
						border: none;
						transform: rotate(-180deg) translateY(50%);
					}
				}

				.b-table-of-contents__list {
					&:after {
						height: 0%;
						transition: height 0.4s ease-in-out;
					}
				}
			}
		}
	}
}
