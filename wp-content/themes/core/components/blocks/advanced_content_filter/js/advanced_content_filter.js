/* -----------------------------------------------------------------------------
 *
 * Component: Advanced Content Filter
 *
 * ----------------------------------------------------------------------------- */

/**
 * @module
 * @description Javascript that drives the Advanced Content Filter block
 */

import * as tools from 'utils/tools';
import { on } from 'utils/events';

const el = {
	contentFilter: tools.getNodes( '[data-js="content-filter"]', true, document, true ),
	counter: 0,
};

const checkTagsCount = ( event ) => {
	const filterContainer = event.target.closest( '.b-advanced-content-filter__filter-container' );
	const tagsContainer = filterContainer.querySelector( '.b-advanced-content-filter__tags' );

	if ( event.target.classList.contains( 'remove-tag' ) ) {
		event.target.closest( '.b-advanced-content-filter__tags__item' ).remove();
	}

	const tags = tagsContainer.querySelectorAll( '.b-advanced-content-filter__tags__item' );
	el.counter = tags.length;

	if ( el.counter >= 5 ) {
		filterContainer.classList.add( 'b-advanced-content-filter__filter-container--disabled' );
	} else {
		filterContainer.classList.remove( 'b-advanced-content-filter__filter-container--disabled' );
	}
};

const updateOpenFilterCount = ( event ) => {
	const openFilterIcon = event.target.closest( '.b-advanced-content-filter' ).querySelector( '.open-filter' );
	const openFilterCount = openFilterIcon?.querySelector( 'span' );

	if ( el.counter === 0 ) {
		if ( openFilterCount ) {
			openFilterCount.remove();
		}

		return;
	}

	if ( openFilterCount ) {
		openFilterCount.innerHTML = el.counter;
	} else {
		const spanCount = document.createElement( 'span' );
		spanCount.textContent = el.counter;
		openFilterIcon.appendChild( spanCount );
	}
};

const closeMobileFilter = ( event ) => {
	const filter = event.target.closest( '.b-advanced-content-filter__filter' );

	if ( filter.classList.contains( 'b-advanced-content-filter__filter--active' ) ) {
		filter.classList.remove( 'b-advanced-content-filter__filter--active' );
	}
};

const applyFilter = ( event ) => {
	event.preventDefault();

	closeMobileFilter( event );

	updateOpenFilterCount( event );
};

const resetFilter = ( event ) => {
	event.preventDefault();

	closeMobileFilter( event );

	const filterContainer = event.target.closest( '.b-advanced-content-filter__filter-container' );
	const tags = filterContainer.querySelectorAll( '.b-advanced-content-filter__tags__item' );

	if ( ! tags ) {
		return;
	}

	tags.forEach( ( tag ) => {
		filterContainer.querySelector( `#${ tag.getAttribute( 'data-tag' ) }` ).checked = false;
		tag.remove();
	} );

	checkTagsCount( event );

	updateOpenFilterCount( event );
};

/**
 * @function removeTag
 * @description Removes a tag from the filter
 */
const removeTag = ( event ) => {
	const tag = event.target.closest( '.b-advanced-content-filter__tags__item' );
	const id = tag.getAttribute( 'data-tag' );
	const filterContainer = tag.closest( '.b-advanced-content-filter__filter-container' );

	checkTagsCount( event );

	const checkbox = filterContainer.querySelector( `#${ id }` );

	checkbox.checked = false;

	checkbox.dispatchEvent( new Event( 'change', { bubbles: true } ) );
};

/**
 * @function manageTags
 * @description Manages the tags that are displayed when a filter is selected
 */
const manageTags = ( event ) => {
	const chosenOption = event.target;
	const optionId = chosenOption.getAttribute( 'id' );
	const optionValue = chosenOption.getAttribute( 'data-label' );
	const filterContainer = chosenOption.closest( '.b-advanced-content-filter__filter-container' );
	const tagsContainer = filterContainer.querySelector( '.b-advanced-content-filter__tags' );
	const tagsHTML = tagsContainer.innerHTML;

	if ( chosenOption.checked ) {
		tagsContainer.innerHTML = tagsHTML + `<div class="b-advanced-content-filter__tags__item" data-tag="${ optionId }">${ optionValue } <span class="remove-tag"></span></div>`;

		const removeIcons = tagsContainer.querySelectorAll( `.remove-tag` );
		removeIcons.forEach( ( removeIcon ) => {
			on( removeIcon, 'click', removeTag );
		} );
	} else {
		const tagToRemove = tagsContainer.querySelector( `[data-tag="${ optionId }"]` );

		if ( tagToRemove ) {
			tagToRemove.remove();
		}
	}

	checkTagsCount( event );
};

const closeFilter = ( event ) => {
	closeMobileFilter( event );
};

const openFilter = ( event ) => {
	const filter = event.target.closest( '.b-advanced-content-filter' ).querySelector( '.b-advanced-content-filter__filter' );
	filter.classList.add( 'b-advanced-content-filter__filter--active' );
};

const bindEvents = () => {
	for ( let i = 0; i < el.contentFilter.length; i++ ) {
		const selectOptions = el.contentFilter[ i ].querySelectorAll( 'input[type="checkbox"]' );
		const applyFilterBtn = el.contentFilter[ i ].querySelector( '#apply-filter' );
		const resetFilterBtn = el.contentFilter[ i ].querySelector( '#reset-filter' );
		const closeFilterBtn = el.contentFilter[ i ].querySelector( '.close-filter' );
		const openFilterBtn = el.contentFilter[ i ].closest( '.b-advanced-content-filter' ).querySelector( '.open-filter' );

		on( applyFilterBtn, 'click', applyFilter );
		on( resetFilterBtn, 'click', resetFilter );
		on( closeFilterBtn, 'click', closeFilter );
		on( openFilterBtn, 'click', openFilter );

		selectOptions.forEach( ( option ) => {
			on( option, 'change', manageTags );
		} );
	}
};

/**
 * @function init
 * @description Initializes the class if the element(s) to work on are found.
 */
const init = () => {
	bindEvents();

	if ( ! el.contentFilter ) {
		return;
	}

	console.info( 'SquareOne Theme: Initialized Advanced Content Filter scripts.' );
};

export default init;
