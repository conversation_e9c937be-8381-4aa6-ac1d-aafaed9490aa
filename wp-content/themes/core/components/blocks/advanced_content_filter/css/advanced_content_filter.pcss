/* -----------------------------------------------------------------------------
 *
 * Advanced Content Filter
 *
 * ----------------------------------------------------------------------------- */

.b-advanced-content-filter {
	overflow: hidden;
	padding-top: 80px;
	margin-top: 0;

	@media (--viewport-medium) {
		padding-top: 120px;
	}

	@media (--viewport-medium-max) {
		min-height: 100vh;
	}

	&__container {
		position: relative;

		@media (--viewport-full) {
			display: grid;
			grid-auto-flow: column;
			grid-template-columns: var(--grid-sidebar-col) 1fr;
			grid-column-gap: var(--spacer-70);
		}
	}

	&__content-container {
		display: grid;
		grid-template-columns: 1fr;
		gap: var(--spacer-60) var(--spacer-40);

		&:has(>p) {
			grid-template-columns: 1fr;
			text-align: center;
		}

		@media (--viewport-large) {
			grid-template-columns: 1fr 1fr;
		}

		@media (--viewport-full) {
			grid-template-columns: 1fr 1fr 1fr;
		}
	}

	.close-filter {
		display: none;

		@media (--viewport-medium-max) {
			display: flex;
			position: absolute;
			right: 0;
			top: 5px;
			cursor: pointer;

			&:before {
				content: var(--icon-close);
				font-family: var(--font-family-core-icons);
				color: var(--color-black);
				font-size: var(--font-size-body-small);
				line-height: 16px;
				transform: scale(1.5);
			}
		}
	}

	.open-filter {
		display: none;

		span {
			position: absolute;
			width: calc(var(--spacer-20) - 2px);
			height: calc(var(--spacer-20) - 2px);
			border-radius: 50%;
			right: 3px;
			top: -4px;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 10px;
			line-height: 10px;
			color: var(--color-white);
			background-color: var(--color-primary);
		}

		@media (--viewport-medium-max) {
			display: flex;
			position: absolute;
			right: var(--grid-margin-small);
			top: 3px;
			cursor: pointer;
		}
	}

	&__filter {
		z-index: 1;
		user-select: none;

		@media (--viewport-medium-max) {
			position: fixed;
			background-color: var(--color-white);
			width: 100%;
			left: 100%;
			top: 0;
			padding: var(--spacer-50) var(--grid-margin-small) var(--spacer-60);
			box-shadow: var(--box-shadow-30);
			transform: translateX(100%);
			transition: all 300ms;
			height: 100%;
			z-index: 31;

			.admin-bar & {
				padding-top: var(--spacer-80);
			}
		}

		&--active {
			transform: translateX(0);
			left: 0;
		}

		&-name {
			font-size: var(--font-size-special-small);
			font-weight: var(--font-weight-bold);
			margin-bottom: var(--spacer-30);

			&:not(:first-child) {
				border-top: solid 1px var(--color-neutral-20);
				padding-top: var(--spacer-40);
				margin-top: var(--spacer-40);
			}

			&:first-child {
				margin-top: -6px;
			}
		}

		&-tooltip {
			display: flex;
			align-items: center;
			gap: calc(var(--spacer-10) / 2);

			.c-tooltip {
				left: 50%;
				transform: translateX(-40%);
				background-color: var(--color-neutral-20);
				box-shadow: none;
				padding: var(--spacer-10) calc(var(--spacer-20) - 3px);
				width: max-content;

				&:after {
					left: 40%;
					border-color: var(--color-neutral-20) transparent transparent;
				}
			}
		}

		&-title {
			h6& {
				.s-sink > & {
					margin: 0;
				}
			}
		}

		&-container {
			@media (--viewport-medium-max) {
				position: relative;
				height: 100%;
        		display: flex;
				flex-direction: column;
				justify-content: space-between;
			}

			&--disabled {
				input[type="checkbox"]:not(:checked) {
					pointer-events: none;
					cursor: default;
				}

				.b-advanced-content-filter__selector__label {
					&:not(:has(input[type="checkbox"]:checked)) {
						pointer-events: none;
						cursor: default;
						opacity: .5;
					}
				}
			}
		}
	}

	&__tags {
		display: flex;
		flex-wrap: wrap;
		column-gap: var(--spacer-20);
		margin-bottom: var(--spacer-40);

		@media (--viewport-medium-max) {
			margin-bottom: var(--spacer-30);
		}

		&__item {
			display: flex;
			gap: var(--spacer-10);
			align-items: center;
			font-size: var(--font-size-special-small);
			color: var(--color-white);
			margin-top: var(--spacer-20);
			padding: 3px var(--spacer-20);
			background-color: var(--color-primary);
			border-radius: var(--form-border-radius);
			cursor: default;

			.remove-tag {
				display: flex;
				cursor: pointer;

				&:before {
					content: var(--icon-close);
					font-family: var(--font-family-core-icons);
					color: var(--color-white);
					font-size: var(--font-size-body-small);
					line-height: 16px;
					transform: scale(1.5);
				}
			}
		}
	}

	&__tooltip {
		position: relative;

		&-icon {
			display: flex;

			&:before {
				content: "";
				width: 12px;
				height: 12px;
				background-image: url(/wp-content/themes/core/assets/img/icons/info.svg);
				background-size: contain;
				background-repeat: no-repeat;
				background-position: center;
			}
		}
	}

	&__selector {
		&__option {
			&:not(:last-child) {
				margin-bottom: var(--spacer-30);
			}

			input[type="checkbox"] {
				-webkit-appearance: none;
				appearance: none;
				background-color: #fff;
				margin: 0;
				font: inherit;
				color: #000;
				width: var(--spacer-30);
				height: var(--spacer-30);
				border: 1px solid var(--color-neutral-30);
				border-radius: var(--form-border-radius);
				display: grid;
				place-content: center;
				cursor: pointer;

				&:before {
					content: "";
					width: var(--spacer-30);
					height: var(--spacer-30);
					transform: scale(0);
					transition: transform .3s ease-in-out;
					background-color: var(--color-primary);
					background-image: url(/wp-content/themes/core/assets/img/icons/check-white.svg);
					background-position: center;
					background-repeat: no-repeat;
					border-radius: var(--form-border-radius);
				}

				&:checked:before {
					transform: scale(1);
				}
			}
		}

		.t-sink &__label {
			display: flex;
			gap: var(--spacer-10);
			cursor: pointer;
			font-weight: var(--font-weight-regular);
			font-size: var(--font-size-body-small);
			margin-bottom: 0;
			align-items: center;
		}

		&__list {
			&:not(:last-of-type) {
				position: relative;
				padding-bottom: calc(var(--spacer-50) + 4px);
				margin-bottom: var(--spacer-50);

				&:after {
					content: "";
					position: absolute;
					bottom: 0;
					left: 0;
					width: calc(100% - var(--spacer-30) - var(--spacer-10));
					height: 1px;
					background: var(--color-neutral-30);
				}
			}
		}
	}

	&__filters {
		max-height: 450px;
		overflow-y: auto;
		padding-right: var(--spacer-20);
		position: relative;
		@mixin scrollbar-thin;

		@media (--viewport-medium-max) {
			max-height: 100%;
		}

		&:after, &:before {
			content: '';
			display: block;
			position: sticky;
			width: 100%;
			left: 0;
			height: var(--spacer-10);
		}

		&:after {
			background: linear-gradient(0deg, rgba(255, 255, 255, 1) 50%, rgba(255, 255, 255, 0) 100%);
			bottom: 0;
		}

		&:before {
			background: linear-gradient(180deg, rgba(255, 255, 255, 1) 50%, rgba(255, 255, 255, 0) 100%);
			top: 0;
		}
	}

	&__ctas {
		display: flex;
		gap: calc(var(--spacer-30) - 4px);
		margin-top: var(--spacer-40);

		button {
			width: 100%;
		}
	}

	&__load-more-container {
		text-align: center;
		height: var(--spacer-50);
		margin-top: var(--spacer-40);
		position: relative;

		.post-loader-spinner {
			position: absolute;
			margin-left: var(--spacer-20);
		}

		.post-loader-spinner {
			.post-loader-no-more-posts & {
				left: 50%;
				margin-left: -12px;
			}
		}

		.a-cta {
			background-color: transparent;
			border-top: none;
			border-left: none;
			border-right: none;
		}
	}

	&__content {
		.c-block__title {
			margin-bottom: var(--spacer-50);
		}

		&-container {

		}
	}

	.c-card__meta-primary-container {
		margin-bottom: var(--spacer-10);
	}

	.c-card__media {
		margin-bottom: var(--spacer-20);
	}

	.c-card__image {
		aspect-ratio: '16:10';

		.c-image__image {
			object-fit: cover;
			width: 100%;
			height: 100%;
		}
	}

	.c-card__description {
		margin-bottom: 0;
	}

	.c-card__title {
		&, &:active, &:hover, &:focus {
			color: var(--color-black);
			text-decoration-color: var(--color-black);
		}
	}

	.post-loader-filter:not(:last-child) {
		margin-bottom: var(--spacer-30);
	}
}

@media (--viewport-medium-max) {
	html:has(.b-advanced-content-filter__filter--active) {
		overflow: hidden;
	}
}