import { complete } from 'utils/events';

const BLOCK_NAMES = [ 'acf/tabs', 'acf/advancedcontentfilter' ];
const ATTRIBUTE_NAME = 'uniqid';

const bindUniqueIdToBlock = ( block ) => {
	if ( BLOCK_NAMES.includes( block.name ) && ! block.attributes[ ATTRIBUTE_NAME ] ) {
		const newUniqId = 'id-' + Date.now().toString( 36 ) + Math.random().toString( 36 ).slice( 2, 5 );

		// eslint-disable-next-line no-undef
		wp.data.dispatch( 'core/block-editor' ).updateBlockAttributes( block.clientId, {
			[ ATTRIBUTE_NAME ]: newUniqId,
		} );
	}

	const innerBlocks = block.innerBlocks;

	if ( innerBlocks ) {
		innerBlocks.forEach( ( innerBlock ) => {
			bindUniqueIdToBlock( innerBlock );
		} );
	}
};

const bindUniqueId = () => {
	// eslint-disable-next-line no-undef
	wp.data.subscribe( () => {
		// eslint-disable-next-line no-undef
		const blocks = wp.data.select( 'core/block-editor' ).getBlocks();

		blocks.forEach( ( block ) => {
			bindUniqueIdToBlock( block );
		} );
	} );
};

/**
 * @function init
 * @description Initialize module
 */
const init = () => {
	if ( ! document.body.classList.contains( 'post-php' ) && ! document.body.classList.contains( 'post-new-php' ) ) {
		return;
	}

	complete( bindUniqueId );
};

export default init;
