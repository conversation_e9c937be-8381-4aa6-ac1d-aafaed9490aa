const timezoneFormat = Object.freeze( {
	WEEKDAY_MONTH_DAY: 'weekday_month_day',
	TIME_AM_PM: 'time_am_pm',
} );

const timezoneLocale = Object.freeze( {
	EN: 'en-US',
} );

/**
 * Format date according to timezone and pre-existing formatting options
 * @param dateStr
 * @param format
 * @param locale
 * @returns {string}
 */
const convertToLocalTimezone = ( dateStr, format, locale = timezoneLocale.EN ) => {
	const date = new Date( dateStr );

	if ( isNaN( date.getTime() ) ) {
		return null;
	}

	if ( format === timezoneFormat.WEEKDAY_MONTH_DAY ) {
		const options = { weekday: 'long', month: 'long', day: 'numeric' };

		if ( locale !== 'en-US' ) {
			return date.toLocaleDateString( locale, options );
		}

		const day = date.getDate();
		let suffix = 'th';

		if ( day % 100 < 11 || day % 100 > 13 ) {
			switch ( day % 10 ) {
				case 1: suffix = 'st'; break;
				case 2: suffix = 'nd'; break;
				case 3: suffix = 'rd'; break;
			}
		}

		const dayWithSuffix = `${ day }${ suffix }`;

		return date.toLocaleDateString( locale, options ).replace( new RegExp( `\\b${ day }\\b` ), dayWithSuffix );
	}

	if ( format === timezoneFormat.TIME_AM_PM ) {
		const hour     = date.getHours() % 12 || 12;
		const minute   = date.getMinutes().toString().padStart( 2, '0' );
		const ampm     = date.getHours() >= 12 ? 'pm' : 'am';
		const timezone = date.toLocaleDateString( locale, { timeZoneName: 'short' } ).split( ' ' )[ 1 ];

		return `${ hour }:${ minute } ${ ampm } ${ timezone }`;
	}

	return null;
};

export { convertToLocalTimezone, timezoneFormat, timezoneLocale };
