import * as tools from 'utils/tools';
import { on, ready } from 'utils/events';

const containers = tools.getNodes( '.post-loader-container', true, document, true );

const disableAllFilters = ( container ) => {
	const filterInputs = tools.getNodes( '.post-loader-filter input', false, container, true );

	if ( ! filterInputs ) {
		return null;
	}

	filterInputs.forEach( ( input ) => {
		if ( typeof ( input.name ) !== 'undefined' && typeof ( input.type ) !== 'undefined' ) {
			if ( [ 'checkbox', 'radio' ].indexOf( input.type ) > -1 ) {
				if ( typeof ( input.checked ) !== 'undefined' ) {
					input.checked = false;
				}
			} else if ( typeof ( input.value ) !== 'undefined' ) {
				input.value = '';
			}
		}
	} );
};

const getParsedFilters = ( container ) => {
	const filterInputs = tools.getNodes( '.post-loader-filter input', false, container, true );

	if ( ! filterInputs ) {
		return null;
	}

	const filters = {};

	filterInputs.forEach( ( input ) => {
		if ( typeof ( input.name ) !== 'undefined' && typeof ( input.type ) !== 'undefined' ) {
			const parent = tools.getParentNode( input, 'post-loader-filter' );

			if ( typeof ( parent ) !== 'undefined' && typeof ( parent.getAttribute( 'data-name' ) ) !== 'undefined' ) {
				const filterName = parent.getAttribute( 'data-name' );

				if ( typeof ( filters[ filterName ] ) === 'undefined' ) {
					filters[ filterName ] = {};
				}

				if ( [ 'checkbox', 'radio' ].indexOf( input.type ) > -1 ) {
					if ( typeof ( input.checked ) !== 'undefined' && input.checked ) {
						filters[ filterName ][ input.name ] = input.checked;
					}
				} else if ( typeof ( input.value ) !== 'undefined' && input.value ) {
					filters[ filterName ][ input.name ] = input.value;
				}

				if ( Object.keys( filters[ filterName ] ).length === 0 ) {
					delete filters[ filterName ];
				}
			}
		}
	} );

	return filters;
};

const loadPosts = ( container, parent, postId, block, nonce, isLoadMore = true ) => {
	const page = isLoadMore ? parseInt( container.getAttribute( 'data-page' ) ) + 1 : 1;
	const filters = getParsedFilters( parent );

	parent.classList.add( 'post-loader-loading' );

	const formData = new FormData();

	formData.append( 'action', 'post_loader' );
	formData.append( 'nonce', nonce );
	formData.append( 'pid', postId );
	formData.append( 'block', block );
	formData.append( 'page', page );
	formData.append( 'filters', JSON.stringify( filters ) );

	fetch( '/wp-admin/admin-ajax.php', {
		method: 'POST',
		body: formData,
	} )
		.then( response => response.json() )
		.then( output => {
			if ( output.success && output.data && output.data.posts_html ) {
				if ( isLoadMore ) {
					container.insertAdjacentHTML( 'beforeend', output.data.posts_html );
					container.setAttribute( 'data-page', page );
				} else {
					container.innerHTML = output.data.posts_html;
				}

				if ( ! output.data.has_more_posts ) {
					parent.classList.add( 'post-loader-no-more-posts' );
				} else {
					parent.classList.remove( 'post-loader-no-more-posts' );
				}
			} else if ( ! output.success && output.data ) {
				container.innerHTML = '<p>' + output.data + '</p>';

				parent.classList.add( 'post-loader-no-more-posts' );
			}

			parent.classList.remove( 'post-loader-loading' );
		} )
		.catch( error => {
			parent.classList.remove( 'post-loader-loading' );

			console.error( 'Request Error:', error );
		} );
};

const bindEvents = () => {
	ready( () => {
		containers.forEach( ( container ) => {
			const postId = container.getAttribute( 'data-pid' );
			const block  = container.getAttribute( 'data-block' );
			const parent = tools.getParentNode( container, 'c-block' );

			if ( postId && block && parent ) {
				const loadMoreTrigger = tools.getNodes( '.post-loader-load-more', false, parent, true );
				const resetFiltersTrigger = tools.getNodes( '.post-loader-reset-filters', false, parent, true );
				const applyFiltersTrigger = tools.getNodes( '.post-loader-apply-filters', false, parent, true );
				const filterInputs = tools.getNodes( '.post-loader-filter:not(.post-loader-filter--trigger-disabled) input', false, parent, true );
				const nonce = tools.getNodes( 'input[name="nonce"]', false, parent, true )[ 0 ];

				if ( loadMoreTrigger.length > 0 && nonce ) {
					on( loadMoreTrigger[ 0 ], 'click', () => {
						loadPosts( container, parent, postId, block, nonce.value );
					} );
				}

				if ( filterInputs.length > 0 && nonce ) {
					filterInputs.forEach( ( filterInput ) => {
						on( filterInput, 'change', () => {
							loadPosts( container, parent, postId, block, nonce.value, false );
						} );
					} );
				}

				if ( resetFiltersTrigger.length > 0 ) {
					on( resetFiltersTrigger[ 0 ], 'click', () => {
						disableAllFilters();

						loadPosts( container, parent, postId, block, nonce.value, false );
					} );
				}

				if ( applyFiltersTrigger.length > 0 ) {
					on( applyFiltersTrigger[ 0 ], 'click', () => {
						loadPosts( container, parent, postId, block, nonce.value, false );
					} );
				}
			}
		} );
	} );
};

/**
 * @function init
 * @description Kick off this modules functions
 */
const postLoader = () => {
	if ( 0 === containers.length ) {
		return;
	}

	bindEvents();

	console.info( 'SquareOne Theme: Initialized global post loader scripts.' );
};

export default postLoader;
