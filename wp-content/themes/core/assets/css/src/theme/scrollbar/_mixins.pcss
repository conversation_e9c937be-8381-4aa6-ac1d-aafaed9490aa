/* -----------------------------------------------------------------------------
 *
 * Mixins: Scrollbar
 *
 * ----------------------------------------------------------------------------- */

/* -------------------------------------------------------------------------
 * Cards
 * ------------------------------------------------------------------------- */

@define-mixin scrollbar-neutral {
	-ms-overflow-style: none;
	scrollbar-width: 8px;
	scrollbar-height: 8px;

	&::-webkit-scrollbar {
		width: 8px;
		height: 8px;
	}

	&::-webkit-scrollbar-track {
		width: 8px;
		height: 8px;
		border-radius: var(--border-radius-base);
	}

	&::-webkit-scrollbar-thumb {
		background-color: var(--color-neutral-20);
		border-radius: var(--border-radius-base);
	}
}

@define-mixin scrollbar-neutral-darker {
	-ms-overflow-style: none;
	scrollbar-width: 8px;
	scrollbar-height: 8px;

	&::-webkit-scrollbar {
		width: 8px;
		height: 8px;
	}

	&::-webkit-scrollbar-track {
		width: 8px;
		height: 8px;
		border-radius: var(--border-radius-base);
		background-color: var(--color-neutral-20);
	}

	&::-webkit-scrollbar-thumb {
		background-color: var(--color-neutral-30);
		border-radius: var(--border-radius-base);
	}
}

@define-mixin scrollbar-transparent-padding {
	&::-webkit-scrollbar-button {
		background: transparent;
		height: var(--spacer-20);
	}

	@media (--viewport-medium-max) {
		&::-webkit-scrollbar-track-piece:start {
			margin-top: var(--spacer-20);
		}

		&::-webkit-scrollbar-track-piece:end {
			margin-bottom: var(--spacer-20);
		}

		&::-webkit-scrollbar-track {
			background-color: transparent;
		}
	}
}

@define-mixin scrollbar-thin {
	&::-webkit-scrollbar {
		width: 8px;
		height: 8px;
	}

	&::-webkit-scrollbar-track {
		width: 8px;
		height: 8px;
		border-radius: var(--border-radius-base);
		background-color: transparent;
		border: solid 1px var(--color-neutral-20);
	}

	&::-webkit-scrollbar-thumb {
		background-color: var(--color-neutral-30);
		border-radius: var(--border-radius-base);
	}
}