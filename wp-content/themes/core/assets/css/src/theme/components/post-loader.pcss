.post-loader-spinner, .post-loader-no-more-posts .post-loader-load-more {
	display: none;
}

.post-loader-loading {
	position: relative;

	&::before {
		content: '';
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 3;
		display: block;
		position: absolute;
		background-color: rgba(255, 255, 255, 0.7);
	}

	.post-loader-spinner {
		width: var(--spacer-30) !important;
		height: var(--spacer-30);
		border: 4px solid var(--color-neutral-30);
		border-bottom-color: var(--color-primary);
		border-radius: 50%;
		display: inline-block;
		box-sizing: border-box;
		animation: spinner-rotation 1s linear infinite;
	}
}

@keyframes spinner-rotation {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}
